-- Add threading support to messages table
ALTER TABLE "messages" ADD COLUMN "threadId" TEXT;
ALTER TABLE "messages" ADD COLUMN "parentId" TEXT;

-- Create index for better performance on thread queries
CREATE INDEX "messages_threadId_idx" ON "messages"("threadId");
CREATE INDEX "messages_parentId_idx" ON "messages"("parentId");

-- Update existing messages to have threadId (each message becomes its own thread initially)
UPDATE "messages" SET "threadId" = "id" WHERE "threadId" IS NULL;
