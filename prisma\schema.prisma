// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  roleId    String?
  role      Role?    @relation(fields: [roleId], references: [id])
  isActive  Boolean  @default(true)
  lastLogin DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String
  password    String
  roleId      String
  role        Role     @relation(fields: [roleId], references: [id])
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?

  @@map("users")
}

model Role {
  id          String       @id @default(cuid())
  name        String       @unique
  description String?
  permissions Permission[]
  users       User[]
  admins      Admin[]
  isSystem    Boolean      @default(false)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@map("roles")
}

model Permission {
  id          String @id @default(cuid())
  name        String @unique
  description String?
  resource    String // e.g., "registrations", "users", "analytics"
  action      String // e.g., "read", "write", "delete", "manage"
  roles       Role[]

  @@map("permissions")
}

model Registration {
  id        String   @id @default(cuid())

  // Personal Information
  fullName     String
  dateOfBirth  DateTime
  gender       String
  address      String
  phoneNumber  String
  emailAddress String

  // Contact Information
  emergencyContactName         String
  emergencyContactRelationship String
  emergencyContactPhone        String

  // Parent/Guardian Information (if under 18)
  parentGuardianName  String?
  parentGuardianPhone String?
  parentGuardianEmail String?

  // Roommate Request (optional)
  roommateRequestConfirmationNumber String?

  // Medical Information
  medications    String? // JSON string or text field
  allergies      String? // JSON string or text field
  specialNeeds   String? // JSON string or text field

  // Dietary Restrictions
  dietaryRestrictions String? // JSON string or text field

  // Parental Permission
  parentalPermissionGranted Boolean @default(false)
  parentalPermissionDate    DateTime?

  // Room allocation
  roomAllocation RoomAllocation?

  // System fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("registrations")
}

model Notification {
  id                String   @id @default(cuid())
  type              String   // e.g., "new_registration", "system_update", "approval_required"
  title             String
  message           String
  priority          String   @default("medium") // "low", "medium", "high"
  isRead            Boolean  @default(false)
  recipientId       String?  // Admin/User ID who should receive this notification
  metadata          String?  // JSON string for additional data
  authorizedBy      String?  // Name of the user who authorized/triggered this action
  authorizedByEmail String?  // Email of the user who authorized/triggered this action
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("notifications")
}

model Setting {
  id          String   @id @default(cuid())
  category    String   // e.g., "userManagement", "security", "notifications", "system"
  key         String   // e.g., "defaultRole", "twoFactor", "newRegistrationAlerts"
  value       String   // JSON string to store any type of value
  type        String   // "text", "email", "select", "toggle", "number"
  options     String?  // JSON array of options for select type
  name        String   // Display name
  description String?  // Optional description
  isSystem    Boolean  @default(false) // System settings that can't be deleted
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([category, key])
  @@map("settings")
}

model Message {
  id              String    @id @default(cuid())
  subject         String
  content         String
  senderEmail     String
  senderName      String
  recipientEmail  String
  recipientName   String
  senderType      String    // "admin" or "user"
  recipientType   String    // "admin" or "user"
  status          String    @default("sent") // "sent", "delivered", "failed"
  error           String?   // Error message if delivery failed
  isDeleted       Boolean   @default(false) // Soft delete flag

  // Threading support
  threadId        String?   // Groups messages in the same conversation
  parentId        String?   // References the message being replied to
  parent          Message?  @relation("MessageReplies", fields: [parentId], references: [id])
  replies         Message[] @relation("MessageReplies")

  sentAt          DateTime  @default(now())
  deliveredAt     DateTime?
  readAt          DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("messages")
}

model Room {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "Room Shiloh", "Room A1"
  gender      String   // "Male" or "Female"
  capacity    Int      // Number of beds/persons the room can contain
  isActive    Boolean  @default(true)
  description String?  // Optional description

  // Room allocations
  allocations RoomAllocation[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("rooms")
}

model RoomAllocation {
  id             String       @id @default(cuid())
  registrationId String       @unique
  roomId         String
  allocatedAt    DateTime     @default(now())
  allocatedBy    String?      // User who performed the allocation

  // Relations
  registration   Registration @relation(fields: [registrationId], references: [id], onDelete: Cascade)
  room          Room         @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("room_allocations")
}
