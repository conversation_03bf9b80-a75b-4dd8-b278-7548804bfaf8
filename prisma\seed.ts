import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create permissions
  const permissions = [
    // Registration permissions
    { name: 'registrations:read', description: 'View registrations', resource: 'registrations', action: 'read' },
    { name: 'registrations:write', description: 'Create and edit registrations', resource: 'registrations', action: 'write' },
    { name: 'registrations:delete', description: 'Delete registrations', resource: 'registrations', action: 'delete' },
    { name: 'registrations:manage', description: 'Full registration management', resource: 'registrations', action: 'manage' },

    // User permissions
    { name: 'users:read', description: 'View users', resource: 'users', action: 'read' },
    { name: 'users:write', description: 'Create and edit users', resource: 'users', action: 'write' },
    { name: 'users:delete', description: 'Delete users', resource: 'users', action: 'delete' },
    { name: 'users:manage', description: 'Full user management', resource: 'users', action: 'manage' },

    // Analytics permissions
    { name: 'analytics:read', description: 'View analytics and reports', resource: 'analytics', action: 'read' },
    { name: 'analytics:export', description: 'Export reports', resource: 'analytics', action: 'export' },

    // Notification permissions
    { name: 'notifications:read', description: 'View notifications', resource: 'notifications', action: 'read' },
    { name: 'notifications:write', description: 'Create notifications', resource: 'notifications', action: 'write' },
    { name: 'notifications:delete', description: 'Delete notifications', resource: 'notifications', action: 'delete' },
    { name: 'notifications:manage', description: 'Full notification management', resource: 'notifications', action: 'manage' },

    // System permissions
    { name: 'system:settings', description: 'Manage system settings', resource: 'system', action: 'settings' },
    { name: 'system:admin', description: 'Full system administration', resource: 'system', action: 'admin' },
  ]

  console.log('Creating permissions...')
  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission,
    })
  }

  // Create roles
  const roles = [
    {
      name: 'Super Admin',
      description: 'Full system access with all permissions',
      isSystem: true,
      permissions: permissions.map(p => p.name), // All permissions
    },
    {
      name: 'Admin',
      description: 'Standard admin with most permissions',
      isSystem: true,
      permissions: [
        'registrations:read',
        'registrations:write',
        'registrations:manage',
        'users:read',
        'users:write',
        'analytics:read',
        'analytics:export',
        'notifications:read',
        'notifications:write',
        'notifications:delete',
        'notifications:manage',
      ],
    },
    {
      name: 'Manager',
      description: 'Program manager with registration and analytics access',
      isSystem: false,
      permissions: [
        'registrations:read',
        'registrations:write',
        'analytics:read',
        'analytics:export',
        'notifications:read',
      ],
    },
    {
      name: 'Staff',
      description: 'Staff member with user management and read access',
      isSystem: false,
      permissions: [
        'registrations:read',
        'analytics:read',
        'users:read',
        'users:write',
        'notifications:read',
      ],
    },
    {
      name: 'Viewer',
      description: 'Read-only access to basic information',
      isSystem: false,
      permissions: [
        'registrations:read',
      ],
    },
  ]

  console.log('Creating roles...')
  for (const roleData of roles) {
    const { permissions: permissionNames, ...roleInfo } = roleData

    const role = await prisma.role.upsert({
      where: { name: roleData.name },
      update: {},
      create: roleInfo,
    })

    // Connect permissions to role
    const permissionsToConnect = await prisma.permission.findMany({
      where: { name: { in: permissionNames } },
    })

    await prisma.role.update({
      where: { id: role.id },
      data: {
        permissions: {
          set: permissionsToConnect.map(p => ({ id: p.id })),
        },
      },
    })
  }

  // Create admin user
  const adminEmail = process.env.ADMIN_EMAIL
  const adminPassword = process.env.ADMIN_PASSWORD

  if (!adminEmail || !adminPassword) {
    console.log('⚠️  ADMIN_EMAIL and ADMIN_PASSWORD environment variables not set.')
    console.log('⚠️  Please set these in your .env file to create the initial admin user.')
    console.log('⚠️  Example: ADMIN_EMAIL=<EMAIL> ADMIN_PASSWORD=your_secure_password')
    console.log('✅ Database seeded successfully (without admin user)!')
    return
  }

  const superAdminRole = await prisma.role.findUnique({
    where: { name: 'Super Admin' },
  })

  const existingAdmin = await prisma.admin.findUnique({
    where: { email: adminEmail }
  })

  if (!existingAdmin) {
    const admin = await prisma.admin.create({
      data: {
        email: adminEmail,
        password: hashPassword(adminPassword),
        name: 'Admin User',
        roleId: superAdminRole?.id
      }
    })

    console.log('✅ Created admin user:', admin.email)
  } else {
    // Update existing admin with Super Admin role
    await prisma.admin.update({
      where: { email: adminEmail },
      data: { roleId: superAdminRole?.id }
    })
    console.log('✅ Updated existing admin with Super Admin role:', existingAdmin.email)
  }

  // Create default settings
  console.log('Creating default settings...')

  const defaultSettings = [
    // User Management Settings
    {
      category: 'userManagement',
      key: 'selfRegistration',
      name: 'Allow Self Registration',
      value: JSON.stringify(false),
      type: 'toggle',
      description: 'Allow users to register themselves without admin approval'
    },
    {
      category: 'userManagement',
      key: 'defaultRole',
      name: 'Default User Role',
      value: JSON.stringify('Viewer'),
      type: 'select',
      options: JSON.stringify(['Viewer', 'Staff', 'Manager']),
      description: 'Default role assigned to new users'
    },
    {
      category: 'userManagement',
      key: 'maxUsers',
      name: 'Maximum Users',
      value: JSON.stringify(1000),
      type: 'number',
      description: 'Maximum number of users allowed in the system'
    },
    {
      category: 'userManagement',
      key: 'passwordRequirements',
      name: 'Password Requirements',
      value: JSON.stringify('Strong'),
      type: 'select',
      options: JSON.stringify(['Weak', 'Medium', 'Strong']),
      description: 'Password complexity requirements for user accounts'
    },
    {
      category: 'userManagement',
      key: 'sessionTimeout',
      name: 'Session Timeout (hours)',
      value: JSON.stringify(4),
      type: 'number',
      description: 'How long user sessions remain active'
    },

    // Security Settings
    {
      category: 'security',
      key: 'twoFactorAuth',
      name: 'Two-Factor Authentication',
      value: JSON.stringify(false),
      type: 'toggle',
      description: 'Require two-factor authentication for all users'
    },
    {
      category: 'security',
      key: 'loginAttempts',
      name: 'Max Login Attempts',
      value: JSON.stringify(5),
      type: 'number',
      description: 'Maximum failed login attempts before account lockout'
    },
    {
      category: 'security',
      key: 'accountLockout',
      name: 'Account Lockout Duration (minutes)',
      value: JSON.stringify(30),
      type: 'number',
      description: 'How long accounts remain locked after failed attempts'
    },
    {
      category: 'security',
      key: 'passwordExpiry',
      name: 'Password Expiry (days)',
      value: JSON.stringify(90),
      type: 'number',
      description: 'How often users must change their passwords'
    },

    // Notification Settings
    {
      category: 'notifications',
      key: 'emailNotifications',
      name: 'Email Notifications',
      value: JSON.stringify(true),
      type: 'toggle',
      description: 'Send email notifications for system events'
    },
    {
      category: 'notifications',
      key: 'adminNotifications',
      name: 'Admin Notifications',
      value: JSON.stringify(true),
      type: 'toggle',
      description: 'Send notifications to administrators'
    },
    {
      category: 'notifications',
      key: 'registrationNotifications',
      name: 'Registration Notifications',
      value: JSON.stringify(true),
      type: 'toggle',
      description: 'Send notifications for new registrations'
    },
    {
      category: 'notifications',
      key: 'notificationRetention',
      name: 'Notification Retention (days)',
      value: JSON.stringify(30),
      type: 'number',
      description: 'How long to keep notifications in the system'
    },

    // System Settings
    {
      category: 'system',
      key: 'systemName',
      name: 'System Name',
      value: JSON.stringify('YouthConnect'),
      type: 'text',
      description: 'Display name for the system'
    },
    {
      category: 'system',
      key: 'timezone',
      name: 'System Timezone',
      value: JSON.stringify('UTC-5 (EST)'),
      type: 'select',
      options: JSON.stringify(['UTC-8 (PST)', 'UTC-7 (MST)', 'UTC-6 (CST)', 'UTC-5 (EST)', 'UTC (GMT)']),
      description: 'Default timezone for the system'
    },
    {
      category: 'system',
      key: 'dateFormat',
      name: 'Date Format',
      value: JSON.stringify('MM/DD/YYYY'),
      type: 'select',
      options: JSON.stringify(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
      description: 'Default date format for the system'
    },
    {
      category: 'system',
      key: 'maintenanceMode',
      name: 'Maintenance Mode',
      value: JSON.stringify(false),
      type: 'toggle',
      description: 'Enable maintenance mode to restrict access'
    },
    {
      category: 'system',
      key: 'debugMode',
      name: 'Debug Mode',
      value: JSON.stringify(false),
      type: 'toggle',
      description: 'Enable debug mode for troubleshooting'
    }
  ]

  for (const setting of defaultSettings) {
    await prisma.setting.upsert({
      where: {
        category_key: {
          category: setting.category,
          key: setting.key
        }
      },
      update: {},
      create: setting
    })
  }

  console.log('✅ Created default settings')

  // Note: Additional users should be created through the admin interface

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
