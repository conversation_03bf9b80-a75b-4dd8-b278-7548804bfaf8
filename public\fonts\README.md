# Fonts Directory

## Adding Apercu Pro Font

To use Apercu Pro font in your application:

1. **Obtain Apercu Pro font files** (these are premium fonts that need to be purchased)

2. **Add the following files to this directory:**
   - `ApercuPro-Regular.woff2`
   - `ApercuPro-Medium.woff2`
   - `ApercuPro-Bold.woff2`

3. **Uncomment the @font-face declarations** in `src/styles/fonts.css`

4. **Update the font stack** in `src/styles/fonts.css` to prioritize Apercu Pro:
   ```css
   .font-apercu {
     font-family: 'Apercu Pro', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
   }
   ```

## Current Setup

The application currently uses **Inter** as the primary font, which is:
- ✅ Free and open source
- ✅ Excellent readability
- ✅ Similar aesthetic to Apercu Pro
- ✅ Optimized for web use
- ✅ Supports all weights and styles

## Font Weights Available

- **400** - Regular
- **500** - Medium  
- **600** - Semi Bold
- **700** - Bold
- **800** - Extra Bold
- **900** - Black

## Notes

- All fonts are loaded with `font-display: swap` for optimal performance
- Text rendering is optimized with antialiasing and ligatures
- The font stack provides excellent fallbacks for all scenarios
