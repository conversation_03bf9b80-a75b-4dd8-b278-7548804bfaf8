import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function createTestUsers() {
  console.log('🔧 Creating test users...')

  try {
    // Get roles
    const superAdminRole = await prisma.role.findUnique({ where: { name: 'Super Admin' } })
    const adminRole = await prisma.role.findUnique({ where: { name: 'Admin' } })
    const managerRole = await prisma.role.findUnique({ where: { name: 'Manager' } })
    const staffRole = await prisma.role.findUnique({ where: { name: 'Staff' } })
    const viewerRole = await prisma.role.findUnique({ where: { name: 'Viewer' } })

    if (!superAdminRole || !adminRole || !managerRole || !staffRole || !viewerRole) {
      console.error('❌ Required roles not found. Please run the seed script first.')
      return
    }

    // Test users to create
    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Manager User',
        password: 'manager123',
        roleId: managerRole.id,
        type: 'user'
      },
      {
        email: '<EMAIL>',
        name: 'Staff User',
        password: 'staff123',
        roleId: staffRole.id,
        type: 'user'
      },
      {
        email: '<EMAIL>',
        name: 'Viewer User',
        password: 'viewer123',
        roleId: viewerRole.id,
        type: 'user'
      }
    ]

    // Create users
    for (const userData of testUsers) {
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      })

      if (!existingUser) {
        const user = await prisma.user.create({
          data: {
            email: userData.email,
            name: userData.name,
            password: hashPassword(userData.password),
            roleId: userData.roleId,
            isActive: true
          },
          include: {
            role: true
          }
        })
        console.log(`✅ Created ${user.role?.name} user: ${user.email}`)
      } else {
        console.log(`⚠️  User already exists: ${userData.email}`)
      }
    }

    // Also create an additional admin user in the Admin table
    const existingAdminUser = await prisma.admin.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!existingAdminUser) {
      const adminUser = await prisma.admin.create({
        data: {
          email: '<EMAIL>',
          name: 'Super Admin User',
          password: hashPassword('superadmin123'),
          roleId: superAdminRole.id,
          isActive: true
        },
        include: {
          role: true
        }
      })
      console.log(`✅ Created additional ${adminUser.role?.name} admin: ${adminUser.email}`)
    } else {
      console.log(`⚠️  Admin user already exists: <EMAIL>`)
    }

    console.log('\n📋 Test User Credentials:')
    console.log('========================')
    console.log('Admin (Admin table):')
    console.log('  Email: <EMAIL>')
    console.log('  Password: admin123')
    console.log('  Role: Super Admin')
    console.log('')
    console.log('Super Admin (Admin table):')
    console.log('  Email: <EMAIL>')
    console.log('  Password: superadmin123')
    console.log('  Role: Super Admin')
    console.log('')
    console.log('Users (User table):')
    console.log('  Manager - <EMAIL> / manager123')
    console.log('  Staff - <EMAIL> / staff123')
    console.log('  Viewer - <EMAIL> / viewer123')
    console.log('')
    console.log('✅ All test users created successfully!')

  } catch (error) {
    console.error('❌ Error creating test users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUsers()
