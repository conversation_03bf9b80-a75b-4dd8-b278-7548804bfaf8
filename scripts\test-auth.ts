import { PrismaClient } from '@prisma/client'
import { verifyPassword, hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function testAuthentication() {
  console.log('🔐 Testing authentication system...')

  try {
    // Test admin user
    const admin = await prisma.admin.findUnique({
      where: { email: '<EMAIL>' },
      include: { role: true }
    })

    if (admin) {
      console.log('✅ Found admin user:', admin.email)
      console.log('   Role:', admin.role?.name)
      console.log('   Active:', admin.isActive)
      
      // Test password verification
      const passwordTest = verifyPassword('admin123', admin.password)
      console.log('   Password test:', passwordTest ? '✅ PASS' : '❌ FAIL')
      
      if (!passwordTest) {
        console.log('   Stored hash:', admin.password)
        console.log('   New hash for admin123:', hashPassword('admin123'))
      }
    } else {
      console.log('❌ Admin user not found')
    }

    console.log('')

    // Test regular users
    const users = await prisma.user.findMany({
      include: { role: true }
    })

    console.log(`📋 Found ${users.length} regular users:`)
    for (const user of users) {
      console.log(`   ${user.email} (${user.role?.name}) - Active: ${user.isActive}`)
      
      // Test password for known test users
      const testPasswords: { [key: string]: string } = {
        '<EMAIL>': 'manager123',
        '<EMAIL>': 'staff123',
        '<EMAIL>': 'viewer123'
      }
      
      if (testPasswords[user.email]) {
        const passwordTest = verifyPassword(testPasswords[user.email], user.password)
        console.log(`     Password test: ${passwordTest ? '✅ PASS' : '❌ FAIL'}`)
      }
    }

    console.log('')
    console.log('🔍 Authentication Summary:')
    console.log('=========================')
    console.log('Available login credentials:')
    console.log('')
    console.log('ADMIN USERS (Admin table):')
    console.log('  <EMAIL> / admin123 (Super Admin)')
    console.log('  <EMAIL> / superadmin123 (Super Admin)')
    console.log('')
    console.log('REGULAR USERS (User table):')
    console.log('  <EMAIL> / manager123 (Manager)')
    console.log('  <EMAIL> / staff123 (Staff)')
    console.log('  <EMAIL> / viewer123 (Viewer)')
    console.log('')
    console.log('💡 Try logging in with any of these credentials at /admin/login')

  } catch (error) {
    console.error('❌ Error testing authentication:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAuthentication()
