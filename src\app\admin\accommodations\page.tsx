'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { parseApiError } from '@/lib/error-messages'
import { useUser } from '@/contexts/UserContext'
import { RoomCard } from '@/components/admin/RoomCard'
import { RoomSetupModal } from '@/components/admin/RoomSetupModal'
import { AllocationSetupModal } from '@/components/admin/AllocationSetupModal'
import { AccommodationSearchExport } from '@/components/admin/AccommodationSearchExport'
import { PersonPreviewModal } from '@/components/admin/PersonPreviewModal'
import {
  Home,
  Users,
  Bed,
  Plus,
  Settings,
  User<PERSON>heck,
  UserX,
  BarChart3,
  Shuffle,
  Eye,
  Loader2
} from 'lucide-react'

interface AccommodationStats {
  totalRegistrations: number
  allocatedRegistrations: number
  unallocatedRegistrations: number
  allocationRate: number
  totalRooms: number
  activeRooms: number
  totalCapacity: number
  occupiedSpaces: number
  availableSpaces: number
  occupancyRate: number
}

interface Room {
  id: string
  name: string
  gender: string
  capacity: number
  isActive: boolean
  description?: string
  occupancy: number
  availableSpaces: number
  occupancyRate: number
  allocations: Array<{
    id: string
    registration: {
      id: string
      fullName: string
      gender: string
      dateOfBirth: string
      phoneNumber: string
      emailAddress: string
    }
  }>
}

export default function AccommodationsPage() {
  const [stats, setStats] = useState<AccommodationStats | null>(null)
  const [roomsByGender, setRoomsByGender] = useState<Record<string, Room[]>>({})
  const [unallocatedByGender, setUnallocatedByGender] = useState<Record<string, any[]>>({})
  const [loading, setLoading] = useState(true)
  const [showRoomModal, setShowRoomModal] = useState(false)
  const [showAllocationModal, setShowAllocationModal] = useState(false)
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showPersonPreview, setShowPersonPreview] = useState(false)
  const [selectedPersonId, setSelectedPersonId] = useState<string | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { currentUser } = useUser()
  const { addToast } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({ title, type })
  }

  const fetchAccommodationData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/accommodations')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch accommodation data')
      }

      const data = await response.json()
      setStats(data.stats)
      setRoomsByGender(data.roomsByGender || {})
      setUnallocatedByGender(data.unallocatedByGender || {})
    } catch (error) {
      console.error('Error fetching accommodation data:', error)
      const errorMessage = parseApiError(error)
      setError(errorMessage.description)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAccommodationData()
  }, [])

  const handleCreateRoom = () => {
    setSelectedRoom(null)
    setShowRoomModal(true)
  }

  const handleEditRoom = (room: Room) => {
    setSelectedRoom(room)
    setShowRoomModal(true)
  }

  const handleRoomSaved = () => {
    setShowRoomModal(false)
    setSelectedRoom(null)
    fetchAccommodationData()
    showToast('Room saved successfully', 'success')
  }

  const handleAutoAllocate = () => {
    setShowAllocationModal(true)
  }

  const handleAllocationComplete = (result: any) => {
    setShowAllocationModal(false)
    fetchAccommodationData()
    setRefreshTrigger(prev => prev + 1)
    showToast(`Successfully allocated ${result.totalAllocated} registrations`, 'success')
  }

  const handlePersonPreview = (registrationId: string) => {
    setSelectedPersonId(registrationId)
    setShowPersonPreview(true)
  }

  const handlePersonPreviewClose = () => {
    setShowPersonPreview(false)
    setSelectedPersonId(null)
  }

  const handleRemoveAllocationFromPreview = () => {
    fetchAccommodationData()
    setRefreshTrigger(prev => prev + 1)
  }

  if (loading) {
    return (
      <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager']}>
        <AdminLayoutNew title="Accommodations" description="Manage room allocations and housing arrangements">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </AdminLayoutNew>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager']}>
      <AdminLayoutNew title="Accommodations" description="Manage room allocations and housing arrangements">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Total Registrations</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{stats?.totalRegistrations || 0}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Allocated</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{stats?.allocatedRegistrations || 0}</p>
                <p className="font-apercu-regular text-xs text-green-600">{stats?.allocationRate || 0}% allocated</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <UserCheck className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Unallocated</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{stats?.unallocatedRegistrations || 0}</p>
                <p className="font-apercu-regular text-xs text-amber-600">Pending allocation</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg flex items-center justify-center">
                <UserX className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Room Occupancy</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{stats?.occupancyRate || 0}%</p>
                <p className="font-apercu-regular text-xs text-gray-600">{stats?.occupiedSpaces || 0}/{stats?.totalCapacity || 0} spaces</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <Button
            onClick={handleCreateRoom}
            className="font-apercu-medium bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New Room
          </Button>

          <Button
            onClick={handleAutoAllocate}
            variant="outline"
            className="font-apercu-medium"
            disabled={!stats?.unallocatedRegistrations}
          >
            <Shuffle className="h-4 w-4 mr-2" />
            Auto Allocate Rooms
          </Button>
        </div>

        {/* Search and Export */}
        <AccommodationSearchExport
          onPersonSelect={handlePersonPreview}
          refreshTrigger={refreshTrigger}
        />

        {/* Rooms by Gender */}
        {Object.entries(roomsByGender).map(([gender, rooms]) => (
          <div key={gender} className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="font-apercu-bold text-xl text-gray-900">{gender} Rooms</h2>
              <Badge variant="outline" className="font-apercu-medium">
                {rooms.length} room{rooms.length !== 1 ? 's' : ''}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rooms.map((room) => (
                <RoomCard
                  key={room.id}
                  room={room}
                  onEdit={handleEditRoom}
                  onRefresh={fetchAccommodationData}
                  onPersonPreview={handlePersonPreview}
                />
              ))}
            </div>
          </div>
        ))}

        {/* Unallocated Registrations */}
        {Object.keys(unallocatedByGender).length > 0 && (
          <div className="mb-8">
            <h2 className="font-apercu-bold text-xl text-gray-900 mb-4">Unallocated Registrations</h2>
            {Object.entries(unallocatedByGender).map(([gender, registrations]) => (
              <Card key={gender} className="p-6 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-apercu-bold text-lg text-gray-900">{gender}</h3>
                  <Badge variant="warning" className="font-apercu-medium">
                    {registrations.length} unallocated
                  </Badge>
                </div>
                <div className="space-y-2">
                  {registrations.slice(0, 5).map((reg) => (
                    <div key={reg.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                      <span className="font-apercu-medium text-sm text-gray-900">{reg.fullName}</span>
                      <span className="font-apercu-regular text-xs text-gray-500">
                        {new Date().getFullYear() - new Date(reg.dateOfBirth).getFullYear()} years old
                      </span>
                    </div>
                  ))}
                  {registrations.length > 5 && (
                    <p className="font-apercu-regular text-sm text-gray-500 pt-2">
                      And {registrations.length - 5} more...
                    </p>
                  )}
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Modals */}
        <RoomSetupModal
          isOpen={showRoomModal}
          onClose={() => setShowRoomModal(false)}
          onSave={handleRoomSaved}
          room={selectedRoom}
        />

        <AllocationSetupModal
          isOpen={showAllocationModal}
          onClose={() => setShowAllocationModal(false)}
          onComplete={handleAllocationComplete}
          unallocatedCount={stats?.unallocatedRegistrations || 0}
        />

        {/* Person Preview Modal */}
        <PersonPreviewModal
          isOpen={showPersonPreview}
          onClose={handlePersonPreviewClose}
          registrationId={selectedPersonId}
          onRemoveAllocation={handleRemoveAllocationFromPreview}
        />

        {/* Error Modal */}
        <ErrorModal
          isOpen={!!error}
          onClose={() => setError(null)}
          type="error"
          title="Error"
          description={error || ''}
        />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
