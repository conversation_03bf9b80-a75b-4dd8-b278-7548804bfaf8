'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { BarChart3, TrendingUp, Users, Calendar } from 'lucide-react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement,
} from 'chart.js'
import { Bar, Doughnut, Line } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement
)

interface AnalyticsData {
  totalRegistrations: number
  demographics: {
    ageGroups: Record<string, number>
    genderDistribution: Record<string, number>
  }
  trends: {
    daily: Array<{ date: string; count: number }>
    monthly: Array<{ month: string; count: number }>
  }
  stats: {
    averageAge: number
    growthRate: number
    peakMonth: string
    completionRate: number
  }
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/admin/analytics')
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data)
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }
  if (loading) {
    return (
      <AdminLayoutNew
        title="Analytics"
        description="Registration trends and program insights"
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <AdminLayoutNew
      title="Analytics"
      description="Registration trends and program insights"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-apercu-medium text-sm text-gray-600 mb-1">Growth Rate</p>
              <p className="font-apercu-bold text-2xl text-gray-900">
                {analytics?.stats.growthRate ? `+${analytics.stats.growthRate}%` : '+0%'}
              </p>
              <Badge className="bg-green-50 text-green-700 border-0 font-apercu-medium text-xs mt-2">
                ↗ Monthly growth
              </Badge>
            </div>
            <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-apercu-medium text-sm text-gray-600 mb-1">Avg. Age</p>
              <p className="font-apercu-bold text-2xl text-gray-900">
                {analytics?.stats.averageAge ? analytics.stats.averageAge.toFixed(1) : '0'}
              </p>
              <Badge className="bg-blue-50 text-blue-700 border-0 font-apercu-medium text-xs mt-2">
                Years old
              </Badge>
            </div>
            <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
              <Users className="h-5 w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-apercu-medium text-sm text-gray-600 mb-1">Peak Month</p>
              <p className="font-apercu-bold text-2xl text-gray-900">
                {analytics?.stats.peakMonth || 'N/A'}
              </p>
              <Badge className="bg-purple-50 text-purple-700 border-0 font-apercu-medium text-xs mt-2">
                Most registrations
              </Badge>
            </div>
            <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
              <Calendar className="h-5 w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-apercu-medium text-sm text-gray-600 mb-1">Total Registrations</p>
              <p className="font-apercu-bold text-2xl text-gray-900">
                {analytics?.totalRegistrations || 0}
              </p>
              <Badge className="bg-indigo-50 text-indigo-700 border-0 font-apercu-medium text-xs mt-2">
                All time
              </Badge>
            </div>
            <div className="h-10 w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Age Distribution Chart */}
        <Card className="p-6">
          <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Age Distribution</h3>
          <div className="h-80">
            {analytics?.demographics.ageGroups ? (
              <Bar
                data={{
                  labels: Object.keys(analytics.demographics.ageGroups),
                  datasets: [
                    {
                      label: 'Number of Participants',
                      data: Object.values(analytics.demographics.ageGroups),
                      backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                      ],
                      borderColor: [
                        'rgba(102, 126, 234, 1)',
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(139, 92, 246, 1)',
                      ],
                      borderWidth: 2,
                      borderRadius: 8,
                      borderSkipped: false,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                    tooltip: {
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      titleColor: 'white',
                      bodyColor: 'white',
                      borderColor: 'rgba(102, 126, 234, 1)',
                      borderWidth: 1,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                      },
                      ticks: {
                        stepSize: 1,
                      },
                    },
                    x: {
                      grid: {
                        display: false,
                      },
                    },
                  },
                }}
              />
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="font-apercu-medium text-gray-500">No age data available</p>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Gender Distribution Chart */}
        <Card className="p-6">
          <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Gender Distribution</h3>
          <div className="h-80">
            {analytics?.demographics.genderDistribution ? (
              <Doughnut
                data={{
                  labels: Object.keys(analytics.demographics.genderDistribution),
                  datasets: [
                    {
                      data: Object.values(analytics.demographics.genderDistribution),
                      backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(236, 72, 153, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                      ],
                      borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(236, 72, 153, 1)',
                        'rgba(16, 185, 129, 1)',
                      ],
                      borderWidth: 2,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                          size: 12,
                        },
                      },
                    },
                    tooltip: {
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      titleColor: 'white',
                      bodyColor: 'white',
                      borderColor: 'rgba(59, 130, 246, 1)',
                      borderWidth: 1,
                    },
                  },
                }}
              />
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="font-apercu-medium text-gray-500">No gender data available</p>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Registration Trends Chart */}
      <Card className="p-6">
        <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">📈 Registration Trends (Last 30 Days)</h3>
        <div className="h-96">
          {analytics?.trends.daily ? (
            <Line
              data={{
                labels: analytics.trends.daily.map(item => {
                  const date = new Date(item.date)
                  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                }),
                datasets: [
                  {
                    label: 'Daily Registrations',
                    data: analytics.trends.daily.map(item => item.count),
                    borderColor: 'rgba(102, 126, 234, 1)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                  },
                ],
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1,
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    grid: {
                      color: 'rgba(0, 0, 0, 0.1)',
                    },
                    ticks: {
                      stepSize: 1,
                    },
                  },
                  x: {
                    grid: {
                      display: false,
                    },
                  },
                },
                interaction: {
                  intersect: false,
                  mode: 'index',
                },
              }}
            />
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="font-apercu-medium text-gray-500">No trend data available</p>
              </div>
            </div>
          )}
        </div>
      </Card>
    </AdminLayoutNew>
  )
}
