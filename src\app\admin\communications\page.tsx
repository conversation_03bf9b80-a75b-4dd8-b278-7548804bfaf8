'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { parseApiError } from '@/lib/error-messages'
import { useUser } from '@/contexts/UserContext'
import { EmailConfigDisplay } from '@/components/admin/EmailConfigDisplay'
import {
  Mail,
  Phone,
  Download,
  Send,
  Users,
  Copy,
  CheckCircle,
  AlertCircle,
  Loader2,
  FileText,
  MessageSquare,
  Filter,
  Search,
  X,
  Eye
} from 'lucide-react'

interface Registration {
  id: string
  fullName: string
  emailAddress: string
  phoneNumber: string
  createdAt: string
  gender: string
  dateOfBirth: string
}

export default function CommunicationsPage() {
  const [registrations, setRegistrations] = useState<Registration[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedEmails, setSelectedEmails] = useState<string[]>([])
  const [selectedPhones, setSelectedPhones] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [genderFilter, setGenderFilter] = useState('')
  const [isExporting, setIsExporting] = useState(false)
  const [isSendingBulkEmail, setIsSendingBulkEmail] = useState(false)
  const [showBulkEmailModal, setShowBulkEmailModal] = useState(false)
  const [showBulkSmsModal, setShowBulkSmsModal] = useState(false)
  const [bulkEmailData, setBulkEmailData] = useState({
    subject: '',
    message: '',
    includeNames: true
  })
  const [bulkSmsData, setBulkSmsData] = useState({
    message: '',
    includeNames: true
  })
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { addToast } = useToast()
  const { currentUser } = useUser()

  // Role-based permissions
  const canSendBulkMessages = currentUser?.role?.name && ['Super Admin', 'Admin', 'Manager'].includes(currentUser.role.name)
  const isStaffUser = currentUser?.role?.name === 'Staff'

  useEffect(() => {
    fetchRegistrations()
  }, [])

  const fetchRegistrations = async () => {
    try {
      const response = await fetch('/api/registrations?limit=1000')
      if (response.ok) {
        const data = await response.json()
        setRegistrations(data.registrations || [])
      } else {
        throw new Error('Failed to fetch registrations')
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Failed to Load Registrations',
        description: 'Unable to fetch registration data. Please refresh the page or contact support.',
        duration: 8000,
        action: {
          label: 'Retry',
          onClick: () => fetchRegistrations()
        }
      })
    } finally {
      setLoading(false)
    }
  }

  // Filter registrations based on search and gender
  const filteredRegistrations = registrations.filter(registration => {
    const matchesSearch = registration.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.emailAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.phoneNumber.includes(searchTerm)
    const matchesGender = genderFilter === '' || registration.gender === genderFilter
    return matchesSearch && matchesGender
  })

  // Get unique emails and phones
  const allEmails = [...new Set(filteredRegistrations.map(r => r.emailAddress).filter(Boolean))]
  const allPhones = [...new Set(filteredRegistrations.map(r => r.phoneNumber).filter(Boolean))]

  const handleSelectAllEmails = () => {
    if (selectedEmails.length === allEmails.length) {
      setSelectedEmails([])
    } else {
      setSelectedEmails([...allEmails])
    }
  }

  const handleSelectAllPhones = () => {
    if (selectedPhones.length === allPhones.length) {
      setSelectedPhones([])
    } else {
      setSelectedPhones([...allPhones])
    }
  }

  const handleEmailToggle = (email: string) => {
    setSelectedEmails(prev =>
      prev.includes(email)
        ? prev.filter(e => e !== email)
        : [...prev, email]
    )
  }

  const handlePhoneToggle = (phone: string) => {
    setSelectedPhones(prev =>
      prev.includes(phone)
        ? prev.filter(p => p !== phone)
        : [...prev, phone]
    )
  }

  const copyToClipboard = async (text: string, type: 'emails' | 'phones') => {
    try {
      await navigator.clipboard.writeText(text)
      addToast({
        type: 'success',
        title: 'Copied to Clipboard',
        description: `${type === 'emails' ? 'Email addresses' : 'Phone numbers'} have been copied to your clipboard.`,
        duration: 3000
      })
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Copy Failed',
        description: 'Unable to copy to clipboard. Please select and copy manually.',
        duration: 5000
      })
    }
  }

  const exportEmails = () => {
    const emailsToExport = selectedEmails.length > 0 ? selectedEmails : allEmails
    const csvContent = emailsToExport.join('\n')

    const blob = new Blob([csvContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const element = document.createElement('a')
    element.href = url
    element.download = `participant-emails-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
    window.URL.revokeObjectURL(url)

    addToast({
      type: 'success',
      title: 'Emails Exported',
      description: `Successfully exported ${emailsToExport.length} email addresses to a text file.`,
      duration: 5000
    })
  }

  const exportPhones = () => {
    const phonesToExport = selectedPhones.length > 0 ? selectedPhones : allPhones
    const csvContent = phonesToExport.join('\n')

    const blob = new Blob([csvContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const element = document.createElement('a')
    element.href = url
    element.download = `participant-phones-${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
    window.URL.revokeObjectURL(url)

    addToast({
      type: 'success',
      title: 'Phone Numbers Exported',
      description: `Successfully exported ${phonesToExport.length} phone numbers to a text file.`,
      duration: 5000
    })
  }

  const handleSendBulkEmail = async () => {
    if (!bulkEmailData.subject || !bulkEmailData.message) {
      addToast({
        type: 'error',
        title: 'Missing Information',
        description: 'Please provide both subject and message for the email.',
        duration: 5000
      })
      return
    }

    if (selectedEmails.length === 0) {
      addToast({
        type: 'error',
        title: 'No Recipients Selected',
        description: 'Please select at least one email address to send the bulk email.',
        duration: 5000
      })
      return
    }

    setIsSendingBulkEmail(true)

    try {
      const response = await fetch('/api/admin/communications/bulk-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipients: selectedEmails,
          subject: bulkEmailData.subject,
          message: bulkEmailData.message,
          includeNames: bulkEmailData.includeNames
        })
      })

      const data = await response.json()

      if (response.ok) {
        addToast({
          type: 'success',
          title: 'Bulk Email Sent Successfully',
          description: `Email sent to ${data.results.successful} of ${data.results.total} recipients. ${data.results.failed > 0 ? `${data.results.failed} emails failed to send.` : ''}`,
          duration: 8000
        })

        // Reset form and close modal
        setBulkEmailData({
          subject: '',
          message: '',
          includeNames: true
        })
        setShowBulkEmailModal(false)
        setSelectedEmails([])
      } else {
        setErrorModal({
          isOpen: true,
          type: 'error',
          title: 'Bulk Email Failed',
          description: data.message || 'Unable to send the bulk email. This could be due to server issues or email configuration problems.',
          details: `Error: ${data.error}\nRecipients: ${selectedEmails.length}\nTime: ${new Date().toISOString()}`,
          errorCode: 'BULK_EMAIL_ERROR'
        })
      }
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Network Error',
        description: 'Unable to send the bulk email due to a network error. Please check your connection and try again.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nRecipients: ${selectedEmails.length}\nTime: ${new Date().toISOString()}`,
        errorCode: 'BULK_EMAIL_NETWORK_ERROR'
      })
    } finally {
      setIsSendingBulkEmail(false)
    }
  }

  const handleSendBulkSms = async () => {
    if (!bulkSmsData.message) {
      addToast({
        type: 'error',
        title: 'Missing Information',
        description: 'Please provide a message for the SMS.',
        duration: 5000
      })
      return
    }

    if (selectedPhones.length === 0) {
      addToast({
        type: 'error',
        title: 'No Recipients Selected',
        description: 'Please select at least one phone number to send the bulk SMS.',
        duration: 5000
      })
      return
    }

    // For now, show a placeholder message since SMS functionality would require SMS service integration
    addToast({
      type: 'info',
      title: 'SMS Feature Coming Soon',
      description: `SMS functionality is being developed. Would send to ${selectedPhones.length} recipients: "${bulkSmsData.message.substring(0, 50)}${bulkSmsData.message.length > 50 ? '...' : ''}"`,
      duration: 8000
    })

    // Reset form and close modal
    setBulkSmsData({
      message: '',
      includeNames: true
    })
    setShowBulkSmsModal(false)
    setSelectedPhones([])
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Communications" description="Manage participant communications and contact information">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager', 'Staff']}>
      <AdminLayoutNew title="Communications" description="Manage participant communications and contact information">
        {/* Staff Access Notice */}
        {isStaffUser && (
          <div className="mb-6 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-amber-500 rounded-lg flex items-center justify-center">
                <Eye className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-apercu-bold text-lg text-amber-900">Staff Access Mode</h3>
                <p className="font-apercu-regular text-sm text-amber-800">
                  You have view and export access to participant communications. Bulk messaging features are restricted to Admin and Manager roles.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Email Configuration */}
        <div className="mb-6">
          <EmailConfigDisplay />
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Total Participants</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{filteredRegistrations.length}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Email Addresses</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{allEmails.length}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <Mail className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Phone Numbers</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{allPhones.length}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Phone className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Selected Contacts</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{selectedEmails.length + selectedPhones.length}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <MessageSquare className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="p-4 lg:p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="flex-1 lg:max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2.5 lg:py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm lg:text-base"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  value={genderFilter}
                  onChange={(e) => setGenderFilter(e.target.value)}
                  className="pl-10 pr-8 py-2.5 lg:py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm lg:text-base"
                >
                  <option value="">All Genders</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                </select>
              </div>
            </div>
          </div>

          {/* Results count */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="font-apercu-regular text-sm text-gray-600">
              Showing {filteredRegistrations.length} of {registrations.length} participants
              {searchTerm && (
                <span className="ml-2">
                  • Filtered by: <span className="font-apercu-medium">"{searchTerm}"</span>
                </span>
              )}
              {genderFilter && (
                <span className="ml-2">
                  • Gender: <span className="font-apercu-medium">{genderFilter}</span>
                </span>
              )}
            </p>
          </div>
        </Card>

        {/* Email Addresses Section */}
        <Card className="p-4 lg:p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <Mail className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-apercu-bold text-lg text-gray-900">Email Addresses</h3>
                <p className="font-apercu-regular text-sm text-gray-600">
                  {allEmails.length} unique email addresses • {selectedEmails.length} selected
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAllEmails}
                className="font-apercu-medium"
              >
                {selectedEmails.length === allEmails.length ? 'Deselect All' : 'Select All'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard((selectedEmails.length > 0 ? selectedEmails : allEmails).join(', '), 'emails')}
                className="font-apercu-medium"
              >
                <Copy className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Copy</span>
              </Button>
              <Button
                size="sm"
                onClick={exportEmails}
                className="font-apercu-medium"
              >
                <Download className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Export</span>
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
            {allEmails.map((email, index) => {
              const registration = filteredRegistrations.find(r => r.emailAddress === email)
              return (
                <div
                  key={index}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedEmails.includes(email)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleEmailToggle(email)}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      selectedEmails.includes(email)
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedEmails.includes(email) && (
                        <CheckCircle className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-apercu-medium text-sm text-gray-900 truncate">{email}</p>
                      {registration && (
                        <p className="font-apercu-regular text-xs text-gray-500 truncate">
                          {registration.fullName}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {allEmails.length === 0 && (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="font-apercu-medium text-gray-500">No email addresses found</p>
              <p className="font-apercu-regular text-sm text-gray-400">
                {searchTerm || genderFilter ? 'Try adjusting your filters' : 'No registrations with email addresses yet'}
              </p>
            </div>
          )}
        </Card>

        {/* Phone Numbers Section */}
        <Card className="p-4 lg:p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Phone className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-apercu-bold text-lg text-gray-900">Phone Numbers</h3>
                <p className="font-apercu-regular text-sm text-gray-600">
                  {allPhones.length} unique phone numbers • {selectedPhones.length} selected
                </p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAllPhones}
                className="font-apercu-medium"
              >
                {selectedPhones.length === allPhones.length ? 'Deselect All' : 'Select All'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard((selectedPhones.length > 0 ? selectedPhones : allPhones).join(', '), 'phones')}
                className="font-apercu-medium"
              >
                <Copy className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Copy</span>
              </Button>
              <Button
                size="sm"
                onClick={exportPhones}
                className="font-apercu-medium"
              >
                <Download className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Export</span>
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
            {allPhones.map((phone, index) => {
              const registration = filteredRegistrations.find(r => r.phoneNumber === phone)
              return (
                <div
                  key={index}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedPhones.includes(phone)
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handlePhoneToggle(phone)}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      selectedPhones.includes(phone)
                        ? 'border-purple-500 bg-purple-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedPhones.includes(phone) && (
                        <CheckCircle className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-apercu-medium text-sm text-gray-900 truncate">{phone}</p>
                      {registration && (
                        <p className="font-apercu-regular text-xs text-gray-500 truncate">
                          {registration.fullName}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {allPhones.length === 0 && (
            <div className="text-center py-8">
              <Phone className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="font-apercu-medium text-gray-500">No phone numbers found</p>
              <p className="font-apercu-regular text-sm text-gray-400">
                {searchTerm || genderFilter ? 'Try adjusting your filters' : 'No registrations with phone numbers yet'}
              </p>
            </div>
          )}
        </Card>

        {/* Bulk Actions */}
        <Card className="p-4 lg:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 mb-6">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Send className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-apercu-bold text-lg text-gray-900">Bulk Actions</h3>
                <p className="font-apercu-regular text-sm text-gray-600">
                  {canSendBulkMessages ? 'Send bulk emails/SMS or export contact lists' : 'Export contact lists (view only)'}
                </p>
              </div>
            </div>
            {isStaffUser && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg px-3 py-2">
                <p className="font-apercu-medium text-xs text-amber-800">
                  Staff Access: View & Export Only
                </p>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <div className="h-6 w-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-xs font-bold">i</span>
              </div>
              <div>
                <h4 className="font-apercu-medium text-sm text-blue-900 mb-2">
                  {canSendBulkMessages ? 'How to Send Bulk Messages' : 'How to Export Contact Lists'}
                </h4>
                {canSendBulkMessages ? (
                  <ol className="font-apercu-regular text-sm text-blue-800 space-y-1 list-decimal list-inside">
                    <li>Select email addresses or phone numbers by clicking on them in the sections above</li>
                    <li>Click "Send Bulk Email" or "Send Bulk SMS" button below (only enabled when contacts are selected)</li>
                    <li>Fill in the subject and message for your communication</li>
                    <li>Optionally enable personalization to include participant names</li>
                    <li>Click "Send" to deliver to all selected recipients</li>
                  </ol>
                ) : (
                  <ol className="font-apercu-regular text-sm text-blue-800 space-y-1 list-decimal list-inside">
                    <li>Use search and filter options to find specific participants</li>
                    <li>Select contacts by clicking on them or use "Select All" buttons</li>
                    <li>Click "Export" buttons to download contact lists</li>
                    <li>Use "Copy" buttons to copy contacts to clipboard</li>
                  </ol>
                )}
                <p className="font-apercu-regular text-xs text-blue-700 mt-2">
                  💡 Tip: Use the search and filter options above to find specific participants before selecting their contacts.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {canSendBulkMessages && (
              <Button
                onClick={() => setShowBulkEmailModal(true)}
                disabled={selectedEmails.length === 0}
                className={`font-apercu-medium h-12 ${
                  selectedEmails.length === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-lg transform hover:scale-105 transition-all duration-200'
                }`}
              >
                <Mail className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">
                  {selectedEmails.length === 0
                    ? 'Send Bulk Email (Select emails first)'
                    : `Send Bulk Email (${selectedEmails.length} recipients)`
                  }
                </span>
                <span className="sm:hidden">
                  Email ({selectedEmails.length})
                </span>
              </Button>
            )}

            {canSendBulkMessages && (
              <Button
                onClick={() => setShowBulkSmsModal(true)}
                disabled={selectedPhones.length === 0}
                variant="outline"
                className={`font-apercu-medium h-12 ${
                  selectedPhones.length === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-lg transform hover:scale-105 transition-all duration-200'
                }`}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">
                  {selectedPhones.length === 0
                    ? 'Send Bulk SMS (Select phones first)'
                    : `Send Bulk SMS (${selectedPhones.length} recipients)`
                  }
                </span>
                <span className="sm:hidden">
                  SMS ({selectedPhones.length})
                </span>
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => {
                const allContacts = `EMAILS:\n${allEmails.join('\n')}\n\nPHONE NUMBERS:\n${allPhones.join('\n')}`
                const blob = new Blob([allContacts], { type: 'text/plain' })
                const url = window.URL.createObjectURL(blob)
                const element = document.createElement('a')
                element.href = url
                element.download = `all-contacts-${new Date().toISOString().split('T')[0]}.txt`
                document.body.appendChild(element)
                element.click()
                document.body.removeChild(element)
                window.URL.revokeObjectURL(url)

                addToast({
                  type: 'success',
                  title: 'All Contacts Exported',
                  description: `Successfully exported ${allEmails.length} emails and ${allPhones.length} phone numbers.`,
                  duration: 5000
                })
              }}
              className="font-apercu-medium h-12"
            >
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export All Contacts</span>
              <span className="sm:hidden">Export All</span>
            </Button>
          </div>
        </Card>

        {/* Bulk Email Modal */}
        {showBulkEmailModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-2xl">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4 rounded-t-lg">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <Send className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-apercu-bold text-xl text-white">Send Bulk Email</h3>
                      <p className="font-apercu-regular text-indigo-100 text-sm">
                        Sending to {selectedEmails.length} recipients
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowBulkEmailModal(false)}
                    className="text-white hover:bg-white/20"
                    disabled={isSendingBulkEmail}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block font-apercu-medium text-sm text-gray-700">
                      Subject *
                    </label>
                    <div className="flex space-x-1">
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({ ...prev, subject: 'Welcome to Youth Program!' }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Welcome
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({ ...prev, subject: 'Important Program Update' }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Update
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({ ...prev, subject: 'Event Reminder' }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Reminder
                      </button>
                    </div>
                  </div>
                  <input
                    type="text"
                    value={bulkEmailData.subject}
                    onChange={(e) => setBulkEmailData(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder="Enter email subject..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={isSendingBulkEmail}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block font-apercu-medium text-sm text-gray-700">
                      Message *
                    </label>
                    <div className="flex space-x-1">
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({
                          ...prev,
                          message: 'We are excited to welcome you to our youth program! Please check your email for important updates and program information.\n\nIf you have any questions, feel free to contact us.\n\nBest regards,\nYouth Program Team'
                        }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Welcome
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({
                          ...prev,
                          message: 'We have an important update regarding our youth program. Please review the information below:\n\n[Add your update details here]\n\nThank you for your attention.\n\nBest regards,\nYouth Program Team'
                        }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Update
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkEmailData(prev => ({
                          ...prev,
                          message: 'This is a friendly reminder about our upcoming event:\n\nDate: [Event Date]\nTime: [Event Time]\nLocation: [Event Location]\n\nPlease make sure to attend. We look forward to seeing you there!\n\nBest regards,\nYouth Program Team'
                        }))}
                        className="text-xs text-indigo-600 hover:text-indigo-800 font-apercu-regular"
                        disabled={isSendingBulkEmail}
                      >
                        Reminder
                      </button>
                    </div>
                  </div>
                  <textarea
                    value={bulkEmailData.message}
                    onChange={(e) => setBulkEmailData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your message..."
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                    disabled={isSendingBulkEmail}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeNames"
                    checked={bulkEmailData.includeNames}
                    onChange={(e) => setBulkEmailData(prev => ({ ...prev, includeNames: e.target.checked }))}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    disabled={isSendingBulkEmail}
                  />
                  <label htmlFor="includeNames" className="font-apercu-regular text-sm text-gray-700">
                    Personalize emails with participant names (e.g., "Dear John Doe,")
                  </label>
                </div>

                {/* Recipients Preview */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-apercu-medium text-sm text-gray-700 mb-2">
                    Recipients ({selectedEmails.length})
                  </h4>
                  <div className="max-h-32 overflow-y-auto">
                    <div className="flex flex-wrap gap-1">
                      {selectedEmails.slice(0, 10).map((email, index) => (
                        <span key={index} className="inline-block bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded font-apercu-regular">
                          {email}
                        </span>
                      ))}
                      {selectedEmails.length > 10 && (
                        <span className="inline-block bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded font-apercu-regular">
                          +{selectedEmails.length - 10} more
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-lg">
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowBulkEmailModal(false)}
                    disabled={isSendingBulkEmail}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendBulkEmail}
                    disabled={isSendingBulkEmail || !bulkEmailData.subject || !bulkEmailData.message}
                    className="font-apercu-medium"
                  >
                    {isSendingBulkEmail ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Send Email
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk SMS Modal */}
        {showBulkSmsModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-2xl">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4 rounded-t-lg">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-apercu-bold text-xl text-white">Send Bulk SMS</h3>
                      <p className="font-apercu-regular text-purple-100 text-sm">
                        Sending to {selectedPhones.length} recipients
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowBulkSmsModal(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block font-apercu-medium text-sm text-gray-700">
                      Message *
                    </label>
                    <div className="flex space-x-1">
                      <button
                        type="button"
                        onClick={() => setBulkSmsData(prev => ({
                          ...prev,
                          message: 'Welcome to our youth program! We are excited to have you join us. Check your email for more details.'
                        }))}
                        className="text-xs text-purple-600 hover:text-purple-800 font-apercu-regular"
                      >
                        Welcome
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkSmsData(prev => ({
                          ...prev,
                          message: 'Important update regarding our youth program. Please check your email for full details.'
                        }))}
                        className="text-xs text-purple-600 hover:text-purple-800 font-apercu-regular"
                      >
                        Update
                      </button>
                      <span className="text-gray-300">|</span>
                      <button
                        type="button"
                        onClick={() => setBulkSmsData(prev => ({
                          ...prev,
                          message: 'Reminder: Youth program event tomorrow. See you there!'
                        }))}
                        className="text-xs text-purple-600 hover:text-purple-800 font-apercu-regular"
                      >
                        Reminder
                      </button>
                    </div>
                  </div>
                  <textarea
                    value={bulkSmsData.message}
                    onChange={(e) => setBulkSmsData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your SMS message..."
                    rows={4}
                    maxLength={160}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                  />
                  <div className="flex justify-between items-center mt-2">
                    <p className="font-apercu-regular text-xs text-gray-500">
                      SMS messages are limited to 160 characters
                    </p>
                    <p className="font-apercu-medium text-xs text-gray-600">
                      {bulkSmsData.message.length}/160
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="sms-include-names"
                    checked={bulkSmsData.includeNames}
                    onChange={(e) => setBulkSmsData(prev => ({ ...prev, includeNames: e.target.checked }))}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <label htmlFor="sms-include-names" className="font-apercu-regular text-sm text-gray-700">
                    Personalize messages with participant names
                  </label>
                </div>

                {/* Recipients Preview */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-apercu-medium text-sm text-gray-700 mb-2">
                    Recipients ({selectedPhones.length})
                  </h4>
                  <div className="max-h-32 overflow-y-auto">
                    <div className="grid grid-cols-2 gap-2">
                      {selectedPhones.slice(0, 10).map((phone, index) => (
                        <div key={index} className="font-apercu-regular text-xs text-gray-600 bg-white px-2 py-1 rounded">
                          {phone}
                        </div>
                      ))}
                      {selectedPhones.length > 10 && (
                        <div className="font-apercu-medium text-xs text-gray-500 bg-white px-2 py-1 rounded">
                          +{selectedPhones.length - 10} more...
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    variant="outline"
                    onClick={() => setShowBulkSmsModal(false)}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendBulkSms}
                    disabled={!bulkSmsData.message}
                    className="font-apercu-medium bg-purple-600 hover:bg-purple-700"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send SMS
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk SMS Modal */}
        {showBulkSmsModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-2xl">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4 rounded-t-lg">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-apercu-bold text-xl text-white">Send Bulk SMS</h3>
                      <p className="font-apercu-regular text-purple-100 text-sm">
                        Sending to {selectedPhones.length} recipients
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowBulkSmsModal(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                <div>
                  <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    value={bulkSmsData.message}
                    onChange={(e) => setBulkSmsData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your SMS message..."
                    rows={4}
                    maxLength={160}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                  />
                  <div className="flex justify-between items-center mt-2">
                    <p className="font-apercu-regular text-xs text-gray-500">
                      SMS messages are limited to 160 characters
                    </p>
                    <p className="font-apercu-medium text-xs text-gray-600">
                      {bulkSmsData.message.length}/160
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    variant="outline"
                    onClick={() => setShowBulkSmsModal(false)}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendBulkSms}
                    disabled={!bulkSmsData.message}
                    className="font-apercu-medium bg-purple-600 hover:bg-purple-700"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send SMS
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Modal */}
        <ErrorModal
          isOpen={errorModal.isOpen}
          onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
          type={errorModal.type}
          title={errorModal.title}
          description={errorModal.description}
          details={errorModal.details}
          errorCode={errorModal.errorCode}
          showRetry={errorModal.type === 'error'}
          onRetry={() => {
            setErrorModal(prev => ({ ...prev, isOpen: false }))
            fetchRegistrations()
          }}
          showContactSupport={errorModal.type === 'error'}
        />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}