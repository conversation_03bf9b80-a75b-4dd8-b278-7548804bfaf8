'use client'

import { useEffect, useState } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { DashboardStats } from '@/components/admin/DashboardStats'
import { RecentRegistrations } from '@/components/admin/RecentRegistrations'
import { NotificationPanel } from '@/components/admin/NotificationPanel'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import {
  TrendingUp,
  Users,
  Calendar,
  Activity,
  ArrowUpRight,
  Clock,
  BarChart3,
  FileText
} from 'lucide-react'

interface Registration {
  id: string
  fullName: string
  emailAddress: string
  phoneNumber: string
  dateOfBirth: string
  createdAt: string
  parentalPermissionGranted: boolean
}

interface DashboardStatsData {
  totalRegistrations: number
  newRegistrations: number
  completedRegistrations: number
  pendingRegistrations: number
  recentActivity: number
}

export default function AdminDashboard() {
  const [registrations, setRegistrations] = useState<Registration[]>([])
  const [stats, setStats] = useState<DashboardStatsData>({
    totalRegistrations: 0,
    newRegistrations: 0,
    completedRegistrations: 0,
    pendingRegistrations: 0,
    recentActivity: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchRegistrations()
  }, [])

  const fetchRegistrations = async () => {
    try {
      const response = await fetch('/api/registrations?limit=1000')
      if (response.ok) {
        const data = await response.json()
        const registrations = data.registrations || []
        setRegistrations(registrations)

        // Calculate stats
        const total = registrations.length
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

        const newRegistrations = registrations.filter((reg: Registration) =>
          new Date(reg.createdAt) > thirtyDaysAgo
        ).length

        const pendingRegistrations = registrations.filter((reg: Registration) =>
          !reg.parentalPermissionGranted
        ).length

        const completedRegistrations = registrations.filter((reg: Registration) =>
          reg.parentalPermissionGranted
        ).length

        // Recent activity (last 24 hours)
        const twentyFourHoursAgo = new Date()
        twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24)
        const recentActivity = registrations.filter((reg: Registration) =>
          new Date(reg.createdAt) > twentyFourHoursAgo
        ).length

        setStats({
          totalRegistrations: total,
          newRegistrations,
          pendingRegistrations,
          completedRegistrations,
          recentActivity
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Dashboard" description="Overview of your youth program">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <AdminLayoutNew title="Dashboard" description="Overview of your youth program">
      {/* Stats Cards */}
      <DashboardStats stats={stats} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Recent Registrations */}
        <div className="lg:col-span-2">
          <RecentRegistrations registrations={registrations} />
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link href="/admin/registrations">
                <Button variant="outline" className="w-full justify-start font-apercu-medium">
                  <Users className="mr-2 h-4 w-4" />
                  View All Registrations
                </Button>
              </Link>
              <Link href="/admin/analytics">
                <Button variant="outline" className="w-full justify-start font-apercu-medium">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Analytics & Reports
                </Button>
              </Link>
              <Link href="/register">
                <Button variant="outline" className="w-full justify-start font-apercu-medium">
                  <FileText className="mr-2 h-4 w-4" />
                  Registration Form
                </Button>
              </Link>
            </div>
          </Card>

          {/* Notifications Panel */}
          <NotificationPanel />
        </div>
      </div>
    </AdminLayoutNew>
  )
}
