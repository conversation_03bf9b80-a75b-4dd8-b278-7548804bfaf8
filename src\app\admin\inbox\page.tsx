'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { useUser } from '@/contexts/UserContext'
import { useMessages } from '@/contexts/MessageContext'
import { Tabs } from '@/components/ui/tabs'
import { MessageModal, MessageListItem, useResponsiveDesign } from '@/components/admin/MessageModal'
import { MessageThreadView } from '@/components/admin/MessageThreadView'
import {
  Mail,
  Search,
  Trash2,
  Reply,
  <PERSON>O<PERSON>,
  Clock,
  User,
  Shield,
  Eye,
  Settings,
  Crown,
  X,
  Send,
  CheckCircle,
  AlertCircle,
  MessageSquare
} from 'lucide-react'

interface Message {
  id: string
  subject: string
  content: string
  senderEmail: string
  senderName: string
  recipientEmail: string
  recipientName: string
  senderType: 'admin' | 'user'
  recipientType: 'admin' | 'user'
  status: string
  sentAt: string
  readAt: string | null
  createdAt: string
  threadId?: string
  parentId?: string
  replies?: Message[]
  parent?: Message
  replyCount?: number
  threadMessages?: Message[]
}

export default function InboxPage() {
  const [allMessages, setAllMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [showThreadModal, setShowThreadModal] = useState(false)
  const { isMobile, isTablet } = useResponsiveDesign()
  const [showReplyModal, setShowReplyModal] = useState(false)
  const [replyContent, setReplyContent] = useState('')
  const [replySubject, setReplySubject] = useState('')
  const [sending, setSending] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [messageToDelete, setMessageToDelete] = useState<Message | null>(null)
  const [deleting, setDeleting] = useState(false)
  const [threadMessages, setThreadMessages] = useState<Message[]>([])
  const [showThread, setShowThread] = useState(false)
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { addToast } = useToast()
  const { currentUser } = useUser()
  const { refreshStats, markAsRead: markMessageAsRead, deleteMessage: deleteMessageFromStats } = useMessages()

  // Handle message click for thread view
  const handleMessageClick = (message: Message) => {
    setSelectedMessage(message)
    if (isMobile || isTablet) {
      setShowThreadModal(true)
    }
    // Mark as read if unread
    if (!message.readAt && message.recipientEmail === currentUser?.email) {
      markAsRead(message.id)
    }
  }

  // Handle thread modal close
  const handleCloseThreadModal = () => {
    setShowThreadModal(false)
    setSelectedMessage(null)
  }

  // Handle reply from thread modal
  const handleThreadReply = async (content: string) => {
    // Refresh messages after reply
    await fetchAllMessages()
    await refreshStats()
  }

  useEffect(() => {
    fetchAllMessages()
  }, [])

  const fetchAllMessages = async () => {
    try {
      setLoading(true)
      console.log('🔄 Fetching all messages (inbox + sent) with optimized parallel requests...')

      // Fetch both inbox and sent messages in parallel
      const [inboxResponse, sentResponse] = await Promise.all([
        fetch('/api/admin/messages/inbox?limit=100'),
        fetch('/api/admin/messages/sent?limit=100')
      ])

      let combinedMessages: Message[] = []

      // Process inbox messages
      if (inboxResponse.ok) {
        const inboxData = await inboxResponse.json()
        console.log('📥 Inbox API response:', inboxData)
        const inboxMessages = inboxData.success ? inboxData.messages : (inboxData.messages || inboxData || [])
        combinedMessages = [...combinedMessages, ...inboxMessages]
        console.log('📥 Added inbox messages:', inboxMessages.length)
      } else {
        console.error('❌ Failed to fetch inbox messages:', inboxResponse.status)
      }

      // Process sent messages
      if (sentResponse.ok) {
        const sentData = await sentResponse.json()
        console.log('📤 Sent API response:', sentData)
        const sentMessages = sentData || []
        combinedMessages = [...combinedMessages, ...sentMessages]
        console.log('📤 Added sent messages:', sentMessages.length)
      } else {
        console.error('❌ Failed to fetch sent messages:', sentResponse.status)
      }

      // Sort all messages by date (newest first) and remove duplicates
      const uniqueMessages = combinedMessages.filter((message, index, self) =>
        index === self.findIndex(m => m.id === message.id)
      )

      const sortedMessages = uniqueMessages.sort((a, b) =>
        new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
      )

      setAllMessages(sortedMessages)
      console.log('✅ Combined and sorted messages:', sortedMessages.length)

      // Refresh message stats after fetching messages
      console.log('📊 Refreshing message stats...')
      refreshStats()
      console.log('✅ All messages fetched successfully with parallel requests')
    } catch (error) {
      console.error('❌ Error in fetchAllMessages:', error)
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Failed to Load Messages',
        description: 'Unable to load your messages. Please refresh the page or contact support.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nTime: ${new Date().toISOString()}`,
        errorCode: 'INBOX_LOAD_ERROR'
      })
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (messageId: string) => {
    try {
      // Use the context function which handles API call and state update
      await markMessageAsRead(messageId)

      // Update local message state
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, readAt: new Date().toISOString() }
          : msg
      ))

      // Refresh stats to ensure accuracy
      await refreshStats()

      addToast({
        type: 'success',
        title: 'Message Marked as Read',
        description: 'The message has been marked as read.',
        duration: 3000
      })
    } catch (error) {
      console.error('Error marking message as read:', error)
      addToast({
        type: 'error',
        title: 'Error',
        description: 'Failed to mark message as read. Please try again.',
        duration: 5000
      })
    }
  }

  const deleteMessage = async (messageId: string) => {
    try {
      // Use the context function which handles API call and state update
      await deleteMessageFromStats(messageId)

      // Refresh all message lists
      await fetchAllMessages()
      setSelectedMessage(null)

      addToast({
        type: 'success',
        title: 'Message Deleted',
        description: 'The message has been moved to deleted messages.',
        duration: 3000
      })
    } catch (error) {
      console.error('Failed to delete message:', error)
      addToast({
        type: 'error',
        title: 'Delete Failed',
        description: 'Unable to delete the message. Please try again.',
        duration: 5000
      })
    }
  }

  const handleReply = async () => {
    if (!selectedMessage || !replyContent.trim()) {
      addToast({
        type: 'error',
        title: 'Missing Information',
        description: 'Please provide a message for the reply.',
        duration: 5000
      })
      return
    }

    setSending(true)

    try {
      const response = await fetch(`/api/admin/messages/${selectedMessage.id}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: replyContent
        })
      })

      if (response.ok) {
        const result = await response.json()
        addToast({
          type: 'success',
          title: 'Reply Sent',
          description: 'Your reply has been sent successfully.',
          duration: 5000
        })
        setShowReplyModal(false)
        setReplyContent('')
        setReplySubject('')
        // Refresh all message lists to update sent messages
        fetchAllMessages()
        // Refresh thread if viewing one
        if (showThread && selectedMessage.threadId) {
          fetchThread(selectedMessage.threadId)
        }
        // Refresh message stats as the recipient will have a new unread message
        refreshStats()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send reply')
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Reply Failed',
        description: error instanceof Error ? error.message : 'Unable to send your reply. Please try again.',
        duration: 5000
      })
    } finally {
      setSending(false)
    }
  }

  const fetchThread = async (threadId: string) => {
    try {
      const response = await fetch(`/api/admin/messages/thread/${threadId}`)
      if (response.ok) {
        const data = await response.json()
        setThreadMessages(data.thread || [])
        setShowThread(true)
      }
    } catch (error) {
      console.error('Error fetching thread:', error)
      addToast({
        type: 'error',
        title: 'Error',
        description: 'Failed to load conversation thread.',
        duration: 5000
      })
    }
  }

  const handleDeleteConfirm = (message: Message) => {
    setMessageToDelete(message)
    setShowDeleteConfirm(true)
  }

  const confirmDelete = async () => {
    if (!messageToDelete) return

    setDeleting(true)
    try {
      // Determine if this should be permanent deletion (from deleted tab) or soft delete
      const isPermanent = activeTab === 'deleted'
      const url = `/api/admin/messages/${messageToDelete.id}/delete${isPermanent ? '?permanent=true' : ''}`

      const response = await fetch(url, {
        method: 'DELETE'
      })

      if (response.ok) {
        const result = await response.json()
        addToast({
          type: 'success',
          title: isPermanent ? 'Message Permanently Deleted' : 'Message Deleted',
          description: isPermanent
            ? 'The message has been permanently removed.'
            : 'The message has been moved to deleted messages.',
          duration: 3000
        })
        setShowDeleteConfirm(false)
        setMessageToDelete(null)
        setSelectedMessage(null)
        fetchAllMessages()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete message')
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Delete Failed',
        description: error instanceof Error ? error.message : 'Unable to delete the message. Please try again.',
        duration: 5000
      })
    } finally {
      setDeleting(false)
    }
  }

  const getRoleIcon = (senderType: string, senderName: string) => {
    // This is a simplified role detection - in a real app you'd have role info
    if (senderName.includes('Super Admin') || senderName.includes('Admin')) {
      return <Crown className="h-4 w-4 text-purple-600" />
    }
    if (senderName.includes('Manager')) {
      return <Settings className="h-4 w-4 text-green-600" />
    }
    if (senderName.includes('Staff')) {
      return <User className="h-4 w-4 text-orange-600" />
    }
    return <Eye className="h-4 w-4 text-gray-600" />
  }

  // Filter all messages based on search term
  const filteredMessages = allMessages.filter(message =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.senderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.recipientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const unreadCount = allMessages.filter(msg => !msg.readAt && msg.recipientEmail === currentUser?.email).length

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Inbox" description="View and manage your messages">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer']}>
      <AdminLayoutNew title="Inbox" description="View and manage your messages">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Total Messages</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{messages.length}</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Unread Messages</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{unreadCount}</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <MailOpen className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Read Messages</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{messages.length - unreadCount}</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">This Week</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">
                  {messages.filter(msg => {
                    const msgDate = new Date(msg.sentAt)
                    const weekAgo = new Date()
                    weekAgo.setDate(weekAgo.getDate() - 7)
                    return msgDate > weekAgo
                  }).length}
                </p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>
        </div>

        {/* Beautiful Unified Messages Header */}
        <Card className="p-6 mb-6 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-indigo-100">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Header Info */}
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="font-apercu-bold text-2xl text-gray-900">All Messages</h2>
                <p className="font-apercu-regular text-gray-600">
                  Sent and received conversations in one place
                </p>
              </div>
            </div>

            {/* Stats and Search */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-3">
                <div className="px-4 py-2 bg-white rounded-lg border border-indigo-200 shadow-sm">
                  <span className="font-apercu-bold text-indigo-600">{filteredMessages.length}</span>
                  <span className="font-apercu-regular text-gray-600 ml-1">total</span>
                </div>
                {allMessages.filter(msg => !msg.readAt && msg.recipientEmail === currentUser?.email).length > 0 && (
                  <div className="px-4 py-2 bg-red-50 rounded-lg border border-red-200 shadow-sm">
                    <span className="font-apercu-bold text-red-600">
                      {allMessages.filter(msg => !msg.readAt && msg.recipientEmail === currentUser?.email).length}
                    </span>
                    <span className="font-apercu-regular text-red-600 ml-1">unread</span>
                  </div>
                )}
              </div>

              {/* Search */}
              <div className="relative w-full sm:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search all messages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 font-apercu-regular bg-white border-gray-200 focus:border-indigo-300 focus:ring-indigo-200"
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Messages Layout - Responsive */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Message List - Responsive */}
          <div className="xl:col-span-1">
            <Card className="p-3 sm:p-4 lg:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <h3 className="font-apercu-bold text-lg text-gray-900 mb-2 sm:mb-0">
                  Recent Conversations ({filteredMessages.length})
                </h3>
                {selectedMessage && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedMessage(null)}
                    className="xl:hidden font-apercu-medium text-gray-600 hover:text-gray-800 w-fit"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Close
                  </Button>
                )}
              </div>

              {filteredMessages.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="font-apercu-medium text-gray-600">No messages found</p>
                  <p className="font-apercu-regular text-sm text-gray-500">
                    {searchTerm ? 'Try adjusting your search criteria' : 'Your inbox is empty'}
                  </p>
                </div>
              ) : (
                <div className="space-y-0 max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                  {filteredMessages.map(message => (
                    <MessageListItem
                      key={message.id}
                      message={message}
                      currentUserEmail={currentUser?.email || ''}
                      onClick={() => handleMessageClick(message)}
                      isSelected={selectedMessage?.id === message.id}
                    />
                  ))}
                </div>
              )}
            </Card>
          </div>

          {/* Message Detail - Desktop Thread View */}
          <div className={`xl:col-span-2 ${selectedMessage ? 'block' : 'hidden xl:block'}`}>
            {selectedMessage ? (
              <Card className="p-0 overflow-hidden">
                <MessageThreadView
                  message={selectedMessage}
                  currentUserEmail={currentUser?.email || ''}
                  onClose={() => setSelectedMessage(null)}
                  onReply={handleThreadReply}
                  isMobile={false}
                />
              </Card>
            ) : (
              <Card className="p-6">
                <div className="text-center py-12">
                  <Mail className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">
                    Select a Message
                  </h3>
                  <p className="font-apercu-regular text-gray-600">
                    Choose a message from the list to view its contents and reply
                  </p>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Reply Modal */}
        {showReplyModal && selectedMessage && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <Reply className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-apercu-bold text-xl text-white">Reply to Message</h3>
                      <p className="font-apercu-regular text-indigo-100 text-sm">
                        Replying to {selectedMessage.senderName}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowReplyModal(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <p className="font-apercu-medium text-sm text-gray-700 mb-1">
                    Replying to:
                  </p>
                  <p className="font-apercu-bold text-gray-900">
                    {selectedMessage.subject}
                  </p>
                  <p className="font-apercu-regular text-sm text-gray-600">
                    From: {selectedMessage.senderName} ({selectedMessage.senderEmail})
                  </p>
                </div>

                <div>
                  <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                    Your Reply *
                  </label>
                  <textarea
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    placeholder="Type your reply here..."
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                  />
                </div>
              </div>

              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowReplyModal(false)}
                    disabled={sending}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleReply}
                    disabled={sending || !replyContent.trim()}
                    className="font-apercu-medium"
                  >
                    {sending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Send Reply
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && messageToDelete && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-apercu-bold text-lg text-gray-900">
                      Delete Message
                    </h3>
                    <p className="font-apercu-regular text-sm text-gray-600">
                      This action cannot be undone
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <p className="font-apercu-regular text-gray-700 mb-2">
                    {activeTab === 'deleted'
                      ? 'Are you sure you want to permanently delete this message? This action cannot be undone.'
                      : 'Are you sure you want to delete this message? It will be moved to your deleted messages.'
                    }
                  </p>
                  <div className="bg-gray-50 p-3 rounded-lg border">
                    <p className="font-apercu-bold text-sm text-gray-900 mb-1">
                      {messageToDelete.subject}
                    </p>
                    <p className="font-apercu-regular text-xs text-gray-600">
                      From: {messageToDelete.senderName}
                    </p>
                  </div>
                  {activeTab === 'deleted' && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="font-apercu-medium text-sm text-red-800">
                        ⚠️ Warning: This will permanently remove the message from the system.
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowDeleteConfirm(false)
                      setMessageToDelete(null)
                    }}
                    disabled={deleting}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={confirmDelete}
                    disabled={deleting}
                    className="font-apercu-medium"
                  >
                    {deleting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Deleting...
                      </>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-2" />
                        {activeTab === 'deleted' ? 'Delete Forever' : 'Delete Message'}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Thread Modal for Mobile/Tablet */}
        <MessageModal
          isOpen={showThreadModal}
          onClose={handleCloseThreadModal}
          message={selectedMessage}
          currentUserEmail={currentUser?.email || ''}
          onReply={handleThreadReply}
        />

        {/* Error Modal */}
        <ErrorModal
          isOpen={errorModal.isOpen}
          onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
          type={errorModal.type}
          title={errorModal.title}
          description={errorModal.description}
          details={errorModal.details}
          errorCode={errorModal.errorCode}
        />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
