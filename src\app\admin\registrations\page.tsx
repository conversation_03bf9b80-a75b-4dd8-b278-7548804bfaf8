'use client'

import { useEffect, useState, useCallback } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { useDebounce, useOptimizedPagination } from '@/lib/performance'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { getErrorMessage, parseApiError } from '@/lib/error-messages'
import { Input } from '@/components/ui/input'
import { Avatar } from '@/components/ui/avatar'
import { ModernDatePicker } from '@/components/ui/modern-date-picker'
import {
  Users,
  Search,
  Eye,
  Download,
  Calendar,
  Mail,
  Phone,
  MapPin,
  User,
  Shield,
  Heart,
  AlertTriangle,
  CheckCircle,
  X,
  ChevronLeft,
  ChevronRight,
  FileText,
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Trash2
} from 'lucide-react'

interface Registration {
  id: string
  fullName: string
  dateOfBirth: string
  gender: string
  address: string
  phoneNumber: string
  emailAddress: string
  emergencyContactName: string
  emergencyContactRelationship: string
  emergencyContactPhone: string
  parentGuardianName?: string
  parentGuardianPhone?: string
  parentGuardianEmail?: string
  roommateRequestConfirmationNumber?: string
  medications?: string
  allergies?: string
  specialNeeds?: string
  dietaryRestrictions?: string
  medicalConditions?: string
  additionalInfo?: string
  parentalPermissionGranted: boolean
  parentalPermissionDate?: string
  createdAt: string
  updatedAt: string
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}

export default function AdminRegistrations() {
  const [registrations, setRegistrations] = useState<Registration[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 300)
  const [selectedRegistration, setSelectedRegistration] = useState<Registration | null>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isSendingEmail, setIsSendingEmail] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editFormData, setEditFormData] = useState<Registration | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [registrationToDelete, setRegistrationToDelete] = useState<Registration | null>(null)
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { addToast } = useToast()



  const fetchRegistrations = useCallback(async () => {
    setLoading(true)
    try {
      // Fetch all registrations for client-side filtering (like user management)
      const response = await fetch('/api/registrations?limit=1000')
      if (response.ok) {
        const data = await response.json()
        setRegistrations(data.registrations || [])
        // Update pagination info based on all registrations
        setPagination(prev => ({
          ...prev,
          total: data.registrations?.length || 0,
          pages: Math.ceil((data.registrations?.length || 0) / prev.limit)
        }))
      }
    } catch (error) {
      // Show error toast for fetch failures
      addToast({
        type: 'error',
        title: 'Failed to Load Registrations',
        description: 'Unable to fetch registration data from the server. Please refresh the page or contact support if the issue persists.',
        duration: 8000,
        action: {
          label: 'Retry',
          onClick: () => fetchRegistrations()
        }
      })
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch registrations on component mount
  useEffect(() => {
    fetchRegistrations()
  }, [fetchRegistrations])

  // Reset to page 1 when search changes
  useEffect(() => {
    if (pagination.page !== 1) {
      setPagination(prev => ({ ...prev, page: 1 }))
    }
  }, [debouncedSearchTerm])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }



  // Filter registrations based on search (client-side like user management)
  const allFilteredRegistrations = registrations.filter(registration => {
    const matchesSearch = debouncedSearchTerm === '' ||
      registration.fullName.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      registration.emailAddress.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      registration.phoneNumber.includes(debouncedSearchTerm)

    return matchesSearch
  })

  // Client-side pagination
  const startIndex = (pagination.page - 1) * pagination.limit
  const endIndex = startIndex + pagination.limit
  const filteredRegistrations = allFilteredRegistrations.slice(startIndex, endIndex)

  // Update pagination info based on filtered results
  const totalFilteredPages = Math.ceil(allFilteredRegistrations.length / pagination.limit)

  // Modal button handlers
  const handleExportPDF = async () => {
    if (!selectedRegistration) return

    setIsExporting(true)
    try {
      // Generate HTML content for PDF
      const htmlContent = generateHTMLContent(selectedRegistration)

      // Create a new window for printing
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(htmlContent)
        printWindow.document.close()

        // Wait for content to load, then print
        printWindow.onload = () => {
          printWindow.print()
          printWindow.close()
        }

        // Fallback: if onload doesn't work, try after a delay
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.print()
            printWindow.close()
          }
        }, 1000)
      } else {
        // Fallback: download as HTML file
        const element = document.createElement('a')
        const file = new Blob([htmlContent], { type: 'text/html' })
        element.href = URL.createObjectURL(file)
        element.download = `registration-${selectedRegistration.fullName.replace(/\s+/g, '-').toLowerCase()}.html`
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
      }

      // Show success toast
      addToast({
        type: 'success',
        title: 'PDF Export Successful',
        description: 'Registration exported successfully! You can save it as PDF from the print dialog that opened.',
        duration: 6000
      })
    } catch (error) {
      // Show error modal
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'PDF Export Failed',
        description: 'Unable to export the registration as PDF. This could be due to browser restrictions or a temporary issue.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nRegistration: ${selectedRegistration?.fullName}\nTime: ${new Date().toISOString()}`,
        errorCode: 'PDF_EXPORT_ERROR'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleSendEmail = async () => {
    if (!selectedRegistration) return

    setIsSendingEmail(true)
    try {
      const response = await fetch('/api/admin/registrations/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          registrationId: selectedRegistration.id,
          recipientEmail: selectedRegistration.emailAddress,
          recipientName: selectedRegistration.fullName
        }),
      })

      if (response.ok) {
        // Show success toast
        addToast({
          type: 'success',
          title: 'Email Sent Successfully',
          description: `Email has been sent to ${selectedRegistration.fullName} at ${selectedRegistration.emailAddress}. They should receive it within a few minutes.`,
          duration: 6000
        })
      } else {
        const errorData = await response.json()
        const errorMessage = parseApiError(errorData.error || 'Failed to send email')

        // Show detailed error modal
        setErrorModal({
          isOpen: true,
          type: errorMessage.type,
          title: errorMessage.title,
          description: errorMessage.description,
          details: `API Response: ${errorData.error}\nStatus: ${response.status}\nRecipient: ${selectedRegistration.emailAddress}`,
          errorCode: `EMAIL_${response.status}`
        })
      }
    } catch (error) {
      console.error('Send email failed:', error)

      // Show network error modal
      const errorMessage = parseApiError(error)
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Network Error',
        description: 'Unable to connect to the email service. Please check your internet connection and try again.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nRecipient: ${selectedRegistration.emailAddress}\nTime: ${new Date().toISOString()}`,
        errorCode: 'EMAIL_NETWORK_ERROR'
      })
    } finally {
      setIsSendingEmail(false)
    }
  }

  // Export all registrations as CSV
  const handleExportCSV = async () => {
    setIsExporting(true)
    try {
      // Create CSV content
      const csvContent = generateCSVContent(allFilteredRegistrations)

      // Create and download CSV file
      const element = document.createElement('a')
      const file = new Blob([csvContent], { type: 'text/csv' })
      element.href = URL.createObjectURL(file)
      element.download = `registrations-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(element)
      element.click()
      document.body.removeChild(element)

      addToast({
        type: 'success',
        title: 'CSV Export Successful',
        description: `Successfully exported ${allFilteredRegistrations.length} registrations to CSV format. The file has been downloaded to your device.`,
        duration: 5000
      })
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'CSV Export Failed',
        description: 'Unable to export registrations to CSV format. This could be due to browser restrictions or insufficient data.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nTotal Records: ${allFilteredRegistrations.length}\nTime: ${new Date().toISOString()}`,
        errorCode: 'CSV_EXPORT_ERROR'
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Export all registrations as PDF
  const handleExportPDFAll = async () => {
    setIsExporting(true)
    try {
      // Generate comprehensive PDF content for all registrations
      const htmlContent = generateBulkPDFContent(allFilteredRegistrations)

      // Create and download PDF
      const element = document.createElement('a')
      const file = new Blob([htmlContent], { type: 'text/html' })
      element.href = URL.createObjectURL(file)
      element.download = `registrations-bulk-${new Date().toISOString().split('T')[0]}.html`
      document.body.appendChild(element)
      element.click()
      document.body.removeChild(element)

      addToast({
        type: 'success',
        title: 'Bulk PDF Export Successful',
        description: `Generated PDF export file for ${allFilteredRegistrations.length} registrations. Open the downloaded HTML file and print to PDF.`,
        duration: 8000,
        action: {
          label: 'How to Print',
          onClick: () => {
            setErrorModal({
              isOpen: true,
              type: 'info',
              title: 'How to Print to PDF',
              description: 'To convert the downloaded HTML file to PDF: 1) Open the downloaded file in your browser, 2) Press Ctrl+P (or Cmd+P on Mac), 3) Select "Save as PDF" as the destination, 4) Click Save.',
              errorCode: 'PDF_INSTRUCTIONS'
            })
          }
        }
      })
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Bulk PDF Export Failed',
        description: 'Unable to generate the bulk PDF export file. This could be due to browser limitations or insufficient memory for large datasets.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nTotal Records: ${allFilteredRegistrations.length}\nTime: ${new Date().toISOString()}`,
        errorCode: 'BULK_PDF_EXPORT_ERROR'
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Delete registration
  const handleDeleteRegistration = async (registration: Registration) => {
    setRegistrationToDelete(registration)
    setShowDeleteConfirm(true)
  }

  const confirmDeleteRegistration = async () => {
    if (!registrationToDelete) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/registrations/${registrationToDelete.id}/delete`, {
        method: 'DELETE',
      })

      if (response.ok) {
        const data = await response.json()

        // Show success toast
        addToast({
          type: 'success',
          title: 'Registration Deleted',
          description: `Successfully deleted registration for ${registrationToDelete.fullName}. All associated data has been permanently removed.`,
          duration: 5000
        })

        // Refresh the registrations list
        await fetchRegistrations()

        // Close modals
        setShowDeleteConfirm(false)
        setSelectedRegistration(null)
        setRegistrationToDelete(null)
      } else {
        const errorData = await response.json()
        setErrorModal({
          isOpen: true,
          type: 'error',
          title: 'Delete Failed',
          description: 'Unable to delete the registration. This could be due to insufficient permissions or the registration being referenced by other data.',
          details: `Error: ${errorData.error}\nRegistration: ${registrationToDelete.fullName}\nID: ${registrationToDelete.id}\nTime: ${new Date().toISOString()}`,
          errorCode: `DELETE_${response.status}`
        })
      }
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Delete Operation Failed',
        description: 'A network error occurred while trying to delete the registration. Please check your connection and try again.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nRegistration: ${registrationToDelete?.fullName}\nTime: ${new Date().toISOString()}`,
        errorCode: 'DELETE_NETWORK_ERROR'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const cancelDeleteRegistration = () => {
    setShowDeleteConfirm(false)
    setRegistrationToDelete(null)
  }

  const handleEditRegistration = () => {
    if (!selectedRegistration) return

    // Set the form data to the current registration
    setEditFormData({ ...selectedRegistration })
    setShowEditModal(true)
  }

  const handleCloseModal = () => {
    setSelectedRegistration(null)
    setIsExporting(false)
    setIsSendingEmail(false)
    setIsEditing(false)
  }

  const handleCloseEditModal = () => {
    setShowEditModal(false)
    setEditFormData(null)
    setIsEditing(false)
  }

  const handleSaveEdit = async () => {
    if (!editFormData) return

    setIsEditing(true)
    try {
      const response = await fetch(`/api/admin/registrations/${editFormData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editFormData),
      })

      if (response.ok) {
        // Refresh the registrations list
        await fetchRegistrations()

        // Update the selected registration if it's the same one
        if (selectedRegistration?.id === editFormData.id) {
          setSelectedRegistration(editFormData)
        }

        // Close the edit modal
        handleCloseEditModal()

        // Show success toast
        addToast({
          type: 'success',
          title: 'Registration Updated',
          description: `Successfully updated registration for ${editFormData.fullName}. All changes have been saved to the system.`,
          duration: 5000
        })
      } else {
        const errorData = await response.json()
        setErrorModal({
          isOpen: true,
          type: 'error',
          title: 'Update Failed',
          description: 'Unable to save the registration changes. This could be due to validation errors or insufficient permissions.',
          details: `Error: ${errorData.error}\nRegistration: ${editFormData.fullName}\nID: ${editFormData.id}\nTime: ${new Date().toISOString()}`,
          errorCode: `UPDATE_${response.status}`
        })
      }
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Update Operation Failed',
        description: 'A network error occurred while trying to save the registration changes. Please check your connection and try again.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nRegistration: ${editFormData?.fullName}\nTime: ${new Date().toISOString()}`,
        errorCode: 'UPDATE_NETWORK_ERROR'
      })
    } finally {
      setIsEditing(false)
    }
  }

  const handleEditFormChange = (field: keyof Registration, value: string | boolean) => {
    if (!editFormData) return

    setEditFormData({
      ...editFormData,
      [field]: value
    })
  }

  // Helper function to generate CSV content for bulk export
  const generateCSVContent = (registrations: Registration[]) => {
    const headers = [
      'Full Name',
      'Date of Birth',
      'Age',
      'Gender',
      'Email Address',
      'Phone Number',
      'Address',
      'Parent/Guardian Name',
      'Parent/Guardian Phone',
      'Parent/Guardian Email',
      'Medications',
      'Allergies',
      'Special Needs',
      'Dietary Restrictions',
      'Registration Date',
      'Registration ID'
    ]

    const csvRows = [
      headers.join(','),
      ...registrations.map(reg => [
        `"${reg.fullName}"`,
        `"${formatDate(reg.dateOfBirth)}"`,
        calculateAge(reg.dateOfBirth),
        `"${reg.gender}"`,
        `"${reg.emailAddress}"`,
        `"${reg.phoneNumber}"`,
        `"${reg.address.replace(/"/g, '""')}"`,
        `"${reg.parentGuardianName || ''}"`,
        `"${reg.parentGuardianPhone || ''}"`,
        `"${reg.parentGuardianEmail || ''}"`,
        `"${(reg.medications || '').replace(/"/g, '""')}"`,
        `"${(reg.allergies || '').replace(/"/g, '""')}"`,
        `"${(reg.specialNeeds || '').replace(/"/g, '""')}"`,
        `"${(reg.dietaryRestrictions || '').replace(/"/g, '""')}"`,
        `"${formatDate(reg.createdAt)}"`,
        `"${reg.id}"`
      ].join(','))
    ]

    return csvRows.join('\n')
  }

  // Helper function to generate bulk PDF content for all registrations
  const generateBulkPDFContent = (registrations: Registration[]) => {
    const registrationCards = registrations.map(registration => `
      <div style="page-break-inside: avoid; margin-bottom: 30px; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
        <div style="border-bottom: 2px solid #6366f1; padding-bottom: 15px; margin-bottom: 20px;">
          <h2 style="color: #6366f1; margin: 0; font-size: 24px; font-weight: bold;">Youth Program Registration</h2>
          <p style="color: #6b7280; margin: 5px 0 0 0; font-size: 14px;">Registration ID: ${registration.id}</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
          <div>
            <h3 style="color: #374151; font-size: 18px; margin-bottom: 10px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px;">Personal Information</h3>
            <p><strong>Name:</strong> ${registration.fullName}</p>
            <p><strong>Date of Birth:</strong> ${formatDate(registration.dateOfBirth)}</p>
            <p><strong>Age:</strong> ${calculateAge(registration.dateOfBirth)} years</p>
            <p><strong>Gender:</strong> ${registration.gender}</p>
          </div>

          <div>
            <h3 style="color: #374151; font-size: 18px; margin-bottom: 10px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px;">Contact Information</h3>
            <p><strong>Email:</strong> ${registration.emailAddress}</p>
            <p><strong>Phone:</strong> ${registration.phoneNumber}</p>
            <p><strong>Address:</strong> ${registration.address}</p>
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
          <div>
            <h3 style="color: #374151; font-size: 18px; margin-bottom: 10px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px;">Parent/Guardian</h3>
            <p><strong>Name:</strong> ${registration.parentGuardianName || 'Not provided'}</p>
            <p><strong>Phone:</strong> ${registration.parentGuardianPhone || 'Not provided'}</p>
            <p><strong>Email:</strong> ${registration.parentGuardianEmail || 'Not provided'}</p>
          </div>

          <div>
            <h3 style="color: #374151; font-size: 18px; margin-bottom: 10px; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px;">Medical Information</h3>
            <p><strong>Medications:</strong> ${registration.medications || 'None'}</p>
            <p><strong>Allergies:</strong> ${registration.allergies || 'None'}</p>
            <p><strong>Special Needs:</strong> ${registration.specialNeeds || 'None'}</p>
            <p><strong>Dietary Restrictions:</strong> ${registration.dietaryRestrictions || 'None'}</p>
          </div>
        </div>

        <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px;">
          <h3 style="color: #374151; font-size: 16px; margin-bottom: 10px;">Registration Information</h3>
          <p><strong>Registration Date:</strong> ${formatDate(registration.createdAt)}</p>
        </div>
      </div>
    `).join('')

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Youth Program Registrations - ${new Date().toLocaleDateString()}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1 { color: #6366f1; text-align: center; margin-bottom: 30px; }
        p { margin: 5px 0; }
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <h1>Youth Program Registrations Export</h1>
    <p style="text-align: center; color: #6b7280; margin-bottom: 40px;">
        Generated on ${new Date().toLocaleDateString()} | Total Registrations: ${registrations.length}
    </p>
    ${registrationCards}
</body>
</html>`
  }

  // Helper function to generate HTML content for PDF export
  const generateHTMLContent = (registration: Registration) => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration - ${registration.fullName}</title>
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 30px;
            background: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #4F46E5;
            padding-bottom: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin: -30px -30px 40px -30px;
        }
        .title {
            font-size: 32px;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .subtitle {
            font-size: 16px;
            margin: 10px 0 5px 0;
            opacity: 0.9;
        }
        .section {
            margin: 30px 0;
            background: #f8fafc;
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #4F46E5;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #4F46E5;
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
        }
        .section-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #4F46E5;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 15px;
        }
        .field {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .field-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .field-value {
            color: #1f2937;
            font-size: 16px;
            word-wrap: break-word;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        .badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            padding: 20px;
            background: #f1f5f9;
            border-radius: 8px;
            font-size: 14px;
            color: #64748b;
        }
        @media print {
            body { margin: 0; padding: 20px; }
            .header { margin: -20px -20px 30px -20px; }
            .section { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Youth Registration Certificate</h1>
            <p class="subtitle">Official Registration Document</p>
            <p class="subtitle">Participant: ${registration.fullName}</p>
            <p class="subtitle">Generated on ${new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}</p>
        </div>

        <div class="section">
            <h2 class="section-title">
                <span class="section-icon">👤</span>
                Personal Information
            </h2>
            <div class="field-grid">
                <div class="field">
                    <div class="field-label">Full Name</div>
                    <div class="field-value">${registration.fullName}</div>
                </div>
                <div class="field">
                    <div class="field-label">Date of Birth</div>
                    <div class="field-value">${formatDate(registration.dateOfBirth)}</div>
                </div>
                <div class="field">
                    <div class="field-label">Age</div>
                    <div class="field-value">${calculateAge(registration.dateOfBirth)} years old</div>
                </div>
                <div class="field">
                    <div class="field-label">Gender</div>
                    <div class="field-value">${registration.gender}</div>
                </div>
                <div class="field">
                    <div class="field-label">Registration Date</div>
                    <div class="field-value">${formatDate(registration.createdAt)}</div>
                </div>
                <div class="field">
                    <div class="field-label">Status</div>
                    <div class="field-value"><span class="badge">Completed</span></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">
                <span class="section-icon">📧</span>
                Contact Information
            </h2>
            <div class="field-grid">
                <div class="field">
                    <div class="field-label">Email Address</div>
                    <div class="field-value">${registration.emailAddress}</div>
                </div>
                <div class="field">
                    <div class="field-label">Phone Number</div>
                    <div class="field-value">${registration.phoneNumber}</div>
                </div>
                <div class="field full-width">
                    <div class="field-label">Home Address</div>
                    <div class="field-value">${registration.address}</div>
                </div>
            </div>
        </div>

        ${registration.parentGuardianName ? `
        <div class="section">
            <h2 class="section-title">
                <span class="section-icon">👨‍👩‍👧‍👦</span>
                Parent/Guardian Information
            </h2>
            <div class="field-grid">
                <div class="field">
                    <div class="field-label">Parent/Guardian Name</div>
                    <div class="field-value">${registration.parentGuardianName}</div>
                </div>
                <div class="field">
                    <div class="field-label">Parent/Guardian Phone</div>
                    <div class="field-value">${registration.parentGuardianPhone}</div>
                </div>
                ${registration.parentGuardianEmail ? `
                <div class="field">
                    <div class="field-label">Parent/Guardian Email</div>
                    <div class="field-value">${registration.parentGuardianEmail}</div>
                </div>
                ` : ''}
            </div>
        </div>
        ` : ''}

        ${registration.medicalConditions || registration.medications || registration.allergies ? `
        <div class="section">
            <h2 class="section-title">
                <span class="section-icon">🏥</span>
                Medical Information
            </h2>
            <div class="field-grid">
                ${registration.medicalConditions ? `
                <div class="field full-width">
                    <div class="field-label">Medical Conditions</div>
                    <div class="field-value">${registration.medicalConditions}</div>
                </div>
                ` : ''}
                ${registration.medications ? `
                <div class="field full-width">
                    <div class="field-label">Current Medications</div>
                    <div class="field-value">${registration.medications}</div>
                </div>
                ` : ''}
                ${registration.allergies ? `
                <div class="field full-width">
                    <div class="field-label">Known Allergies</div>
                    <div class="field-value">${registration.allergies}</div>
                </div>
                ` : ''}
            </div>
        </div>
        ` : ''}

        ${registration.additionalInfo ? `
        <div class="section">
            <h2 class="section-title">
                <span class="section-icon">📝</span>
                Additional Information
            </h2>
            <div class="field-grid">
                <div class="field full-width">
                    <div class="field-label">Additional Notes</div>
                    <div class="field-value">${registration.additionalInfo}</div>
                </div>
            </div>
        </div>
        ` : ''}

        <div class="footer">
            <p><strong>Youth Registration System</strong></p>
            <p>This document serves as official confirmation of registration.</p>
            <p>Document ID: REG-${registration.id.slice(-8).toUpperCase()} | Generated: ${new Date().toISOString()}</p>
        </div>
    </div>
</body>
</html>`
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Registrations" description="Manage youth program registrations">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <AdminLayoutNew title="Registrations" description="Manage youth program registrations">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
        <Card className="p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Total Registrations</p>
              <p className="font-apercu-bold text-xl lg:text-2xl text-gray-900">{pagination.total}</p>
            </div>
            <div className="h-8 w-8 lg:h-10 lg:w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Users className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
            </div>
          </div>
        </Card>



        <Card className="p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Today</p>
              <p className="font-apercu-bold text-xl lg:text-2xl text-gray-900">
                {registrations.filter(r => {
                  const regDate = new Date(r.createdAt)
                  const today = new Date()
                  return regDate.toDateString() === today.toDateString()
                }).length}
              </p>
            </div>
            <div className="h-8 w-8 lg:h-10 lg:w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Calendar className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">This Week</p>
              <p className="font-apercu-bold text-xl lg:text-2xl text-gray-900">
                {registrations.filter(r => {
                  const regDate = new Date(r.createdAt)
                  const today = new Date()
                  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
                  return regDate >= weekAgo && regDate <= today
                }).length}
              </p>
            </div>
            <div className="h-8 w-8 lg:h-10 lg:w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <UserCheck className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Average Age</p>
              <p className="font-apercu-bold text-xl lg:text-2xl text-gray-900">
                {registrations.length > 0 ?
                  Math.round(registrations.reduce((sum, r) => sum + calculateAge(r.dateOfBirth), 0) / registrations.length)
                  : 0} years
              </p>
            </div>
            <div className="h-8 w-8 lg:h-10 lg:w-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <UserX className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-4 lg:p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 lg:max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 lg:py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm lg:text-base"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <Button
              variant="outline"
              className="font-apercu-medium text-sm lg:text-base"
              onClick={handleExportCSV}
              disabled={isExporting || allFilteredRegistrations.length === 0}
              size="sm"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              <span className="hidden sm:inline">{isExporting ? 'Exporting...' : 'Export CSV'}</span>
              <span className="sm:hidden">{isExporting ? 'Exporting...' : 'CSV'}</span>
            </Button>

            <Button
              variant="outline"
              className="font-apercu-medium text-sm lg:text-base"
              onClick={handleExportPDFAll}
              disabled={isExporting || allFilteredRegistrations.length === 0}
              size="sm"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <FileText className="h-4 w-4 mr-2" />
              )}
              <span className="hidden sm:inline">{isExporting ? 'Exporting...' : 'Export PDF'}</span>
              <span className="sm:hidden">{isExporting ? 'Exporting...' : 'PDF'}</span>
            </Button>
          </div>
        </div>

        {/* Results count */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="font-apercu-regular text-sm text-gray-600">
            {loading ? (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600 mr-2"></div>
                Loading registrations...
              </span>
            ) : (
              <>
                Showing {allFilteredRegistrations.length > 0 ? startIndex + 1 : 0}-{Math.min(endIndex, allFilteredRegistrations.length)} of {allFilteredRegistrations.length} registrations
                {searchTerm && (
                  <span className="ml-2">
                    • Filtered by: <span className="font-apercu-medium">"{searchTerm}"</span>
                  </span>
                )}
              </>
            )}
          </p>
        </div>
      </Card>

      {/* Registrations Grid */}
      {filteredRegistrations.length > 0 ? (
        <div className={`grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 mb-6 lg:mb-8 transition-opacity duration-200 ${loading ? 'opacity-50' : 'opacity-100'}`}>
          {filteredRegistrations.map((registration) => (
            <Card key={registration.id} className="p-4 lg:p-6 hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-start justify-between mb-4">
                <Avatar className="h-10 w-10 lg:h-12 lg:w-12 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-apercu-bold text-xs lg:text-sm">
                    {getInitials(registration.fullName)}
                  </span>
                </Avatar>
              </div>

              <div className="mb-4">
                <h3 className="font-apercu-bold text-base lg:text-lg text-gray-900 mb-2 line-clamp-2">
                  {registration.fullName}
                </h3>
                <div className="space-y-1.5 lg:space-y-2">
                  <div className="flex items-center text-xs lg:text-sm text-gray-600">
                    <User className="h-3 w-3 lg:h-4 lg:w-4 mr-2 text-gray-400 flex-shrink-0" />
                    <span className="font-apercu-regular">
                      Age {calculateAge(registration.dateOfBirth)} • {registration.gender}
                    </span>
                  </div>
                  <div className="flex items-center text-xs lg:text-sm text-gray-600">
                    <Mail className="h-3 w-3 lg:h-4 lg:w-4 mr-2 text-gray-400 flex-shrink-0" />
                    <span className="font-apercu-regular truncate">
                      {registration.emailAddress}
                    </span>
                  </div>
                  <div className="flex items-center text-xs lg:text-sm text-gray-600">
                    <Phone className="h-3 w-3 lg:h-4 lg:w-4 mr-2 text-gray-400 flex-shrink-0" />
                    <span className="font-apercu-regular">
                      {registration.phoneNumber}
                    </span>
                  </div>
                  <div className="flex items-center text-xs lg:text-sm text-gray-600">
                    <Calendar className="h-3 w-3 lg:h-4 lg:w-4 mr-2 text-gray-400 flex-shrink-0" />
                    <span className="font-apercu-regular">
                      <span className="hidden sm:inline">Registered </span>{formatDate(registration.createdAt)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 font-apercu-medium text-xs lg:text-sm"
                  onClick={() => setSelectedRegistration(registration)}
                >
                  <Eye className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                  <span className="hidden sm:inline">View Details</span>
                  <span className="sm:hidden">View</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="font-apercu-medium text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 px-2 lg:px-3"
                  onClick={() => handleDeleteRegistration(registration)}
                >
                  <Trash2 className="h-3 w-3 lg:h-4 lg:w-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className={`p-12 text-center mb-8 transition-opacity duration-200 ${loading ? 'opacity-50' : 'opacity-100'}`}>
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">Loading Registrations</h3>
              <p className="font-apercu-regular text-gray-600">Please wait while we fetch the registration data...</p>
            </>
          ) : (
            <>
              <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">
                {pagination.total === 0 ? 'No Registrations Yet' : 'No Matching Registrations'}
              </h3>
              <p className="font-apercu-regular text-gray-600 mb-4">
                {pagination.total === 0
                  ? 'When youth register for your program, they will appear here.'
                  : 'Try adjusting your search or filter criteria to find registrations.'
                }
              </p>
              {pagination.total === 0 && (
                <Button className="font-apercu-medium">
                  <FileText className="h-4 w-4 mr-2" />
                  View Registration Form
                </Button>
              )}
            </>
          )}
        </Card>
      )}

      {/* Pagination */}
      {totalFilteredPages > 1 && (
        <Card className="p-4">
          <div className="flex justify-between items-center">
            <div className="font-apercu-regular text-sm text-gray-700">
              Showing page {pagination.page} of {totalFilteredPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="font-apercu-medium"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === totalFilteredPages}
                className="font-apercu-medium"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Registration Details Modal - Enhanced Responsive Design */}
      {selectedRegistration && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-start justify-center p-1 sm:p-4">
          <div className="relative w-full max-w-[95vw] sm:max-w-6xl lg:max-w-7xl max-h-[99vh] sm:max-h-[95vh] bg-white rounded-none sm:rounded-lg lg:rounded-2xl shadow-2xl overflow-hidden my-0 sm:my-4">
            {/* Modal Header - Fully Responsive */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                  <Avatar className="h-7 w-7 sm:h-8 sm:w-8 lg:h-10 lg:w-10 bg-white/20 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-apercu-bold text-xs sm:text-sm">
                      {getInitials(selectedRegistration.fullName)}
                    </span>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-apercu-bold text-sm sm:text-lg lg:text-xl text-white truncate">
                      {selectedRegistration.fullName}
                    </h3>
                    <p className="font-apercu-regular text-indigo-100 text-xs sm:text-sm hidden sm:block">
                      Registration Details
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCloseModal}
                  className="text-white hover:bg-white/20 flex-shrink-0 ml-1 sm:ml-2 p-1 sm:p-2"
                  disabled={isExporting || isSendingEmail || isEditing}
                >
                  <X className="h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </div>
            </div>

            {/* Modal Content - Enhanced Mobile Responsiveness */}
            <div className="max-h-[calc(99vh-80px)] sm:max-h-[calc(90vh-120px)] overflow-y-auto">
              <div className="p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6">
                {/* Personal Information */}
                <div>
                  <h4 className="font-apercu-bold text-sm sm:text-base lg:text-lg text-gray-900 mb-2 sm:mb-3 lg:mb-4 flex items-center">
                    <User className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 mr-1 sm:mr-2 text-indigo-600" />
                    Personal Information
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 lg:gap-4">
                    <div className="bg-gray-50 p-2 sm:p-3 lg:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Full Name</label>
                      <p className="font-apercu-regular text-xs sm:text-sm lg:text-base text-gray-900 break-words">{selectedRegistration.fullName}</p>
                    </div>
                    <div className="bg-gray-50 p-2 sm:p-3 lg:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Date of Birth</label>
                      <p className="font-apercu-regular text-xs sm:text-sm lg:text-base text-gray-900">
                        {formatDate(selectedRegistration.dateOfBirth)} <span className="text-gray-600 text-xs">(Age: {calculateAge(selectedRegistration.dateOfBirth)})</span>
                      </p>
                    </div>
                    <div className="bg-gray-50 p-2 sm:p-3 lg:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Gender</label>
                      <Badge className={`text-xs ${selectedRegistration.gender === 'Male' ? 'bg-blue-50 text-blue-700' : 'bg-pink-50 text-pink-700'} border-0`}>
                        {selectedRegistration.gender}
                      </Badge>
                    </div>
                    <div className="bg-gray-50 p-2 sm:p-3 lg:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Registration Date</label>
                      <p className="font-apercu-regular text-xs sm:text-sm lg:text-base text-gray-900">{formatDate(selectedRegistration.createdAt)}</p>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div>
                  <h4 className="font-apercu-bold text-base sm:text-lg text-gray-900 mb-3 sm:mb-4 flex items-center">
                    <Mail className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-indigo-600" />
                    Contact Information
                  </h4>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                    <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Email Address</label>
                      <p className="font-apercu-regular text-sm sm:text-base text-gray-900 break-all">{selectedRegistration.emailAddress}</p>
                    </div>
                    <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Phone Number</label>
                      <p className="font-apercu-regular text-sm sm:text-base text-gray-900">{selectedRegistration.phoneNumber}</p>
                    </div>
                    <div className="bg-gray-50 p-3 sm:p-4 rounded-lg lg:col-span-2">
                      <label className="block font-apercu-medium text-xs sm:text-sm text-gray-600 mb-1">Address</label>
                      <p className="font-apercu-regular text-sm sm:text-base text-gray-900 break-words">{selectedRegistration.address}</p>
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div>
                  <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Emergency Contact
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                      <label className="block font-apercu-medium text-sm text-red-700 mb-1">Contact Name</label>
                      <p className="font-apercu-regular text-red-900">{selectedRegistration.emergencyContactName}</p>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                      <label className="block font-apercu-medium text-sm text-red-700 mb-1">Relationship</label>
                      <p className="font-apercu-regular text-red-900">{selectedRegistration.emergencyContactRelationship}</p>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg border border-red-200 md:col-span-2">
                      <label className="block font-apercu-medium text-sm text-red-700 mb-1">Phone Number</label>
                      <p className="font-apercu-regular text-red-900">{selectedRegistration.emergencyContactPhone}</p>
                    </div>
                  </div>
                </div>

                {/* Parent/Guardian Information */}
                {(selectedRegistration.parentGuardianName || selectedRegistration.parentGuardianPhone || selectedRegistration.parentGuardianEmail) && (
                  <div>
                    <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                      <Shield className="h-5 w-5 mr-2 text-blue-600" />
                      Parent/Guardian Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedRegistration.parentGuardianName && (
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <label className="block font-apercu-medium text-sm text-blue-700 mb-1">Name</label>
                          <p className="font-apercu-regular text-blue-900">{selectedRegistration.parentGuardianName}</p>
                        </div>
                      )}
                      {selectedRegistration.parentGuardianPhone && (
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <label className="block font-apercu-medium text-sm text-blue-700 mb-1">Phone</label>
                          <p className="font-apercu-regular text-blue-900">{selectedRegistration.parentGuardianPhone}</p>
                        </div>
                      )}
                      {selectedRegistration.parentGuardianEmail && (
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 md:col-span-2">
                          <label className="block font-apercu-medium text-sm text-blue-700 mb-1">Email</label>
                          <p className="font-apercu-regular text-blue-900">{selectedRegistration.parentGuardianEmail}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Medical & Dietary Information */}
                {(selectedRegistration.medications || selectedRegistration.allergies || selectedRegistration.specialNeeds || selectedRegistration.dietaryRestrictions) && (
                  <div>
                    <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                      <Heart className="h-5 w-5 mr-2 text-green-600" />
                      Medical & Dietary Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedRegistration.medications && (
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <label className="block font-apercu-medium text-sm text-green-700 mb-1">Medications</label>
                          <p className="font-apercu-regular text-green-900">{selectedRegistration.medications}</p>
                        </div>
                      )}
                      {selectedRegistration.allergies && (
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <label className="block font-apercu-medium text-sm text-green-700 mb-1">Allergies</label>
                          <p className="font-apercu-regular text-green-900">{selectedRegistration.allergies}</p>
                        </div>
                      )}
                      {selectedRegistration.specialNeeds && (
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <label className="block font-apercu-medium text-sm text-green-700 mb-1">Special Needs</label>
                          <p className="font-apercu-regular text-green-900">{selectedRegistration.specialNeeds}</p>
                        </div>
                      )}
                      {selectedRegistration.dietaryRestrictions && (
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <label className="block font-apercu-medium text-sm text-green-700 mb-1">Dietary Restrictions</label>
                          <p className="font-apercu-regular text-green-900">{selectedRegistration.dietaryRestrictions}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Registration Information */}
                <div>
                  <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-purple-600" />
                    Registration Information
                  </h4>
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="block font-apercu-medium text-sm text-purple-700 mb-1">Registration Date</label>
                        <p className="font-apercu-regular text-purple-900">{formatDate(selectedRegistration.createdAt)}</p>
                      </div>
                      <div className="text-right">
                        <label className="block font-apercu-medium text-sm text-purple-700 mb-1">Registration ID</label>
                        <p className="font-apercu-regular text-purple-900 text-xs">{selectedRegistration.id}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="border-t border-gray-200 px-4 sm:px-6 py-3 sm:py-4 bg-gray-50">
                <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <Button
                      variant="outline"
                      className="font-apercu-medium text-sm"
                      onClick={handleExportPDF}
                      disabled={isExporting}
                      size="sm"
                    >
                      {isExporting ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Download className="h-4 w-4 mr-2" />
                      )}
                      <span className="hidden sm:inline">{isExporting ? 'Exporting...' : 'Export PDF'}</span>
                      <span className="sm:hidden">{isExporting ? 'Exporting...' : 'PDF'}</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="font-apercu-medium text-sm"
                      onClick={handleSendEmail}
                      disabled={isSendingEmail}
                      size="sm"
                    >
                      {isSendingEmail ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Mail className="h-4 w-4 mr-2" />
                      )}
                      <span className="hidden sm:inline">{isSendingEmail ? 'Sending...' : 'Send Email'}</span>
                      <span className="sm:hidden">{isSendingEmail ? 'Sending...' : 'Email'}</span>
                    </Button>
                  </div>
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <Button
                      variant="outline"
                      className="font-apercu-medium text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 text-sm"
                      onClick={() => handleDeleteRegistration(selectedRegistration)}
                      disabled={isExporting || isSendingEmail || isEditing}
                      size="sm"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline">Delete Registration</span>
                      <span className="sm:hidden">Delete</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleCloseModal}
                      className="font-apercu-medium text-sm"
                      disabled={isExporting || isSendingEmail || isEditing}
                      size="sm"
                    >
                      Close
                    </Button>
                    <Button
                      className="font-apercu-medium text-sm"
                      onClick={handleEditRegistration}
                      disabled={isEditing}
                      size="sm"
                    >
                      {isEditing ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <FileText className="h-4 w-4 mr-2" />
                      )}
                      <span className="hidden sm:inline text-white">{isEditing ? 'Loading...' : 'Edit Registration'}</span>
                      <span className="text-white sm:hidden">{isEditing ? 'Loading...' : 'Edit'}</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Registration Modal */}
      {showEditModal && editFormData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden">
            {/* Edit Modal Header */}
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 bg-white/20 flex items-center justify-center">
                    <span className="text-white font-apercu-bold text-sm">
                      {getInitials(editFormData.fullName)}
                    </span>
                  </Avatar>
                  <div>
                    <h3 className="font-apercu-bold text-xl text-white">
                      Edit Registration
                    </h3>
                    <p className="font-apercu-regular text-green-100 text-sm">
                      {editFormData.fullName}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCloseEditModal}
                  className="text-white hover:bg-white/20"
                  disabled={isEditing}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Edit Modal Content */}
            <div className="max-h-[calc(90vh-180px)] overflow-y-auto">
              <div className="p-6 space-y-6">
                {/* Personal Information Section */}
                <div>
                  <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                    <User className="h-5 w-5 mr-2 text-green-600" />
                    Personal Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Full Name
                      </label>
                      <Input
                        value={editFormData.fullName}
                        onChange={(e) => handleEditFormChange('fullName', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                    <div>
                      <ModernDatePicker
                        label="Date of Birth"
                        value={editFormData.dateOfBirth}
                        onChange={(date) => handleEditFormChange('dateOfBirth', date)}
                        placeholder="Select date of birth"
                        disabled={isEditing}
                        maxDate={new Date().toISOString().split('T')[0]}
                        className="font-apercu-regular"
                      />
                    </div>
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Gender
                      </label>
                      <select
                        value={editFormData.gender}
                        onChange={(e) => handleEditFormChange('gender', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        disabled={isEditing}
                      >
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div>
                  <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                    <Mail className="h-5 w-5 mr-2 text-blue-600" />
                    Contact Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Email Address
                      </label>
                      <Input
                        type="email"
                        value={editFormData.emailAddress}
                        onChange={(e) => handleEditFormChange('emailAddress', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <Input
                        value={editFormData.phoneNumber}
                        onChange={(e) => handleEditFormChange('phoneNumber', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Address
                      </label>
                      <Input
                        value={editFormData.address}
                        onChange={(e) => handleEditFormChange('address', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                  </div>
                </div>

                {/* Emergency Contact Section */}
                <div>
                  <h4 className="font-apercu-bold text-lg text-gray-900 mb-4 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Emergency Contact
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Contact Name
                      </label>
                      <Input
                        value={editFormData.emergencyContactName}
                        onChange={(e) => handleEditFormChange('emergencyContactName', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Relationship
                      </label>
                      <Input
                        value={editFormData.emergencyContactRelationship}
                        onChange={(e) => handleEditFormChange('emergencyContactRelationship', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                    <div>
                      <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <Input
                        value={editFormData.emergencyContactPhone}
                        onChange={(e) => handleEditFormChange('emergencyContactPhone', e.target.value)}
                        className="font-apercu-regular"
                        disabled={isEditing}
                      />
                    </div>
                  </div>
                </div>


              </div>
            </div>

            {/* Edit Modal Footer */}
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  <span className="font-apercu-medium">Registration ID:</span> {editFormData.id}
                </div>
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleCloseEditModal}
                    className="font-apercu-medium"
                    disabled={isEditing}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveEdit}
                    className="font-apercu-medium bg-green-600 hover:bg-green-700"
                    disabled={isEditing}
                  >
                    {isEditing ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    {isEditing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && registrationToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl overflow-hidden">
            {/* Delete Modal Header */}
            <div className="bg-gradient-to-r from-red-600 to-red-700 px-6 py-4">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Trash2 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-apercu-bold text-xl text-white">
                    Delete Registration
                  </h3>
                  <p className="font-apercu-regular text-red-100 text-sm">
                    This action cannot be undone
                  </p>
                </div>
              </div>
            </div>

            {/* Delete Modal Content */}
            <div className="p-6">
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <h4 className="font-apercu-bold text-lg text-gray-900 mb-2">
                  Are you sure you want to delete this registration?
                </h4>
                <p className="font-apercu-regular text-gray-600 mb-4">
                  You are about to permanently delete the registration for:
                </p>
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <p className="font-apercu-bold text-gray-900">{registrationToDelete.fullName}</p>
                  <p className="font-apercu-regular text-sm text-gray-600">{registrationToDelete.emailAddress}</p>
                  <p className="font-apercu-regular text-sm text-gray-600">Registered on {formatDate(registrationToDelete.createdAt)}</p>
                </div>
                <p className="font-apercu-regular text-sm text-red-600">
                  This action cannot be undone. All registration data will be permanently removed.
                </p>
              </div>
            </div>

            {/* Delete Modal Footer */}
            <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={cancelDeleteRegistration}
                  className="font-apercu-medium"
                  disabled={isDeleting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmDeleteRegistration}
                  className="font-apercu-medium bg-red-600 hover:bg-red-700 text-white"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4 mr-2" />
                  )}
                  {isDeleting ? 'Deleting...' : 'Delete Registration'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Modal */}
      <ErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
        type={errorModal.type}
        title={errorModal.title}
        description={errorModal.description}
        details={errorModal.details}
        errorCode={errorModal.errorCode}
        showRetry={errorModal.type === 'error'}
        onRetry={() => {
          setErrorModal(prev => ({ ...prev, isOpen: false }))
          if (selectedRegistration) {
            handleSendEmail()
          }
        }}
        showContactSupport={errorModal.type === 'error'}
      />
    </AdminLayoutNew>
  )
}
