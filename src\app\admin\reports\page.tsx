'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useUser } from '@/contexts/UserContext'
import { FileText, Download, Calendar, Users, BarChart3, Loader2, TrendingUp, TrendingDown } from 'lucide-react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement,
} from 'chart.js'
import { Bar, Doughnut, Line } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  LineElement,
  PointElement
)

interface AnalyticsData {
  overview: {
    totalRegistrations: number
    registrationsToday: number
    registrationsThisWeek: number
    registrationsThisMonth: number
    averageAge: number
  }
  demographics: {
    ageGroups: Record<string, number>
    genderDistribution: Record<string, number>
  }
  trends: {
    dailyRegistrations: Array<{ date: string; count: number }>
  }
}

export default function ReportsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [customReport, setCustomReport] = useState({
    dateRange: 'last30days',
    reportType: 'summary',
    format: 'html'
  })
  const { currentUser } = useUser()

  // Check if user can download reports
  const canDownload = currentUser?.role?.name && ['Super Admin', 'Admin', 'Manager'].includes(currentUser.role.name)

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/admin/analytics')
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async (reportType: string, format: string = 'html') => {
    setGenerating(true)
    try {
      const response = await fetch('/api/admin/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType,
          dateRange: customReport.dateRange,
          format
        }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Failed to generate report')
      }
    } catch (error) {
      console.error('Report generation failed:', error)
      alert('Failed to generate report')
    } finally {
      setGenerating(false)
    }
  }

  const reports = [
    {
      name: 'Registration Summary',
      description: 'Complete overview of all registrations',
      type: 'HTML',
      reportType: 'summary',
      icon: Users,
      color: 'from-blue-500 to-cyan-600'
    },
    {
      name: 'Analytics Report',
      description: 'Trends and statistical analysis',
      type: 'HTML',
      reportType: 'analytics',
      icon: BarChart3,
      color: 'from-green-500 to-emerald-600'
    },
    {
      name: 'Demographics Report',
      description: 'Age, gender, and demographic data',
      type: 'HTML',
      reportType: 'demographics',
      icon: FileText,
      color: 'from-purple-500 to-pink-600'
    },
    {
      name: 'PDF Summary',
      description: 'Professional PDF report',
      type: 'PDF',
      reportType: 'summary',
      icon: FileText,
      color: 'from-red-500 to-orange-600'
    },
    {
      name: 'CSV Export',
      description: 'Export all data in CSV format',
      type: 'CSV',
      reportType: 'summary',
      icon: Download,
      color: 'from-indigo-500 to-purple-600'
    }
  ]

  if (loading) {
    return (
      <AdminLayoutNew title="Reports" description="Generate and download program reports">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <AdminLayoutNew
      title="Reports"
      description="Generate and download program reports"
    >
      {/* Analytics Overview */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Total Registrations</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{analytics.overview.totalRegistrations}</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Today</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{analytics.overview.registrationsToday}</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">This Week</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{analytics.overview.registrationsThisWeek}</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">This Month</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{analytics.overview.registrationsThisMonth}</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Average Age</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{analytics.overview.averageAge}</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Quick Reports */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {reports.map((report, index) => (
          <Card key={index} className="p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-start justify-between mb-4">
              <div className={`h-12 w-12 bg-gradient-to-r ${report.color} rounded-xl flex items-center justify-center`}>
                <report.icon className="h-6 w-6 text-white" />
              </div>
              <Badge variant="secondary" className="font-apercu-medium text-xs">
                {report.type}
              </Badge>
            </div>

            <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">
              {report.name}
            </h3>
            <p className="font-apercu-regular text-sm text-gray-600 mb-4">
              {report.description}
            </p>

            <div className="flex items-center justify-between">
              <span className="font-apercu-regular text-xs text-gray-500">
                {canDownload ? 'Click to generate' : 'View only'}
              </span>
              {canDownload ? (
                <Button
                  size="sm"
                  variant="outline"
                  className="font-apercu-medium"
                  onClick={() => generateReport(report.reportType, report.type.toLowerCase())}
                  disabled={generating}
                >
                  {generating ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  {generating ? 'Generating...' : 'Generate'}
                </Button>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  className="font-apercu-medium"
                  disabled
                >
                  <FileText className="h-4 w-4 mr-2" />
                  View Only
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>

      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="font-apercu-bold text-lg text-gray-900">Custom Report Generator</h3>
            <p className="font-apercu-regular text-sm text-gray-600">Create custom reports with specific filters</p>
          </div>
          {canDownload ? (
            <Button
              className="font-apercu-medium"
              onClick={() => generateReport(customReport.reportType, customReport.format)}
              disabled={generating}
            >
              {generating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <FileText className="h-4 w-4 mr-2" />
              )}
              {generating ? 'Generating...' : 'Generate Report'}
            </Button>
          ) : (
            <Button
              className="font-apercu-medium"
              disabled
            >
              <FileText className="h-4 w-4 mr-2" />
              View Only Access
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="font-apercu-medium text-sm text-gray-700 mb-2 block">Date Range</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-lg font-apercu-regular text-sm"
              value={customReport.dateRange}
              onChange={(e) => setCustomReport({...customReport, dateRange: e.target.value})}
            >
              <option value="last7days">Last 7 days</option>
              <option value="last30days">Last 30 days</option>
              <option value="last3months">Last 3 months</option>
              <option value="last6months">Last 6 months</option>
            </select>
          </div>

          <div>
            <label className="font-apercu-medium text-sm text-gray-700 mb-2 block">Report Type</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-lg font-apercu-regular text-sm"
              value={customReport.reportType}
              onChange={(e) => setCustomReport({...customReport, reportType: e.target.value})}
            >
              <option value="summary">Registration Summary</option>
              <option value="demographics">Demographics</option>
              <option value="analytics">Analytics</option>
            </select>
          </div>

          <div>
            <label className="font-apercu-medium text-sm text-gray-700 mb-2 block">Format</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-lg font-apercu-regular text-sm"
              value={customReport.format}
              onChange={(e) => setCustomReport({...customReport, format: e.target.value})}
            >
              <option value="html">HTML</option>
              <option value="pdf">PDF</option>
              <option value="csv">CSV</option>
              <option value="json">JSON</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Demographics Overview */}
      {analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          <Card className="p-6">
            <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Age Distribution</h3>
            <div className="space-y-3">
              {Object.entries(analytics.demographics.ageGroups).map(([ageGroup, count]) => (
                <div key={ageGroup} className="flex items-center justify-between">
                  <span className="font-apercu-medium text-sm text-gray-700">{ageGroup} years</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(count / analytics.overview.totalRegistrations) * 100}%` }}
                      ></div>
                    </div>
                    <span className="font-apercu-regular text-sm text-gray-600 w-8">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Gender Distribution</h3>
            <div className="space-y-3">
              {Object.entries(analytics.demographics.genderDistribution).map(([gender, count]) => (
                <div key={gender} className="flex items-center justify-between">
                  <span className="font-apercu-medium text-sm text-gray-700 capitalize">{gender}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${gender === 'male' ? 'bg-blue-600' : 'bg-pink-600'}`}
                        style={{ width: `${(count / analytics.overview.totalRegistrations) * 100}%` }}
                      ></div>
                    </div>
                    <span className="font-apercu-regular text-sm text-gray-600 w-8">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Interactive Charts Section */}
      {analytics && (
        <div className="mt-8">
          <h2 className="font-apercu-bold text-2xl text-gray-900 mb-6">📊 Interactive Analytics</h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Age Distribution Chart */}
            <Card className="p-6">
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Age Distribution</h3>
              <div style={{ height: '300px' }}>
                <Bar
                  data={{
                    labels: Object.keys(analytics.demographics.ageGroups),
                    datasets: [
                      {
                        label: 'Number of Participants',
                        data: Object.values(analytics.demographics.ageGroups),
                        backgroundColor: [
                          'rgba(102, 126, 234, 0.8)',
                          'rgba(118, 75, 162, 0.8)',
                          'rgba(79, 172, 254, 0.8)',
                          'rgba(0, 242, 254, 0.8)',
                        ],
                        borderColor: [
                          'rgba(102, 126, 234, 1)',
                          'rgba(118, 75, 162, 1)',
                          'rgba(79, 172, 254, 1)',
                          'rgba(0, 242, 254, 1)',
                        ],
                        borderWidth: 2,
                        borderRadius: 8,
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false,
                      },
                      title: {
                        display: false,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          stepSize: 1,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>

            {/* Gender Distribution Chart */}
            <Card className="p-6">
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Gender Distribution</h3>
              <div style={{ height: '300px' }}>
                <Doughnut
                  data={{
                    labels: ['Male', 'Female'],
                    datasets: [
                      {
                        data: [
                          analytics.demographics.genderDistribution.male,
                          analytics.demographics.genderDistribution.female,
                        ],
                        backgroundColor: [
                          'rgba(79, 172, 254, 0.8)',
                          'rgba(240, 147, 251, 0.8)',
                        ],
                        borderColor: [
                          'rgba(79, 172, 254, 1)',
                          'rgba(240, 147, 251, 1)',
                        ],
                        borderWidth: 2,
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          padding: 20,
                          usePointStyle: true,
                        },
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </div>

          {/* Registration Trends Chart */}
          <Card className="p-6">
            <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Registration Trends (Last 30 Days)</h3>
            <div style={{ height: '400px' }}>
              <Line
                data={{
                  labels: analytics.trends.dailyRegistrations.map(item =>
                    new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                  ),
                  datasets: [
                    {
                      label: 'Daily Registrations',
                      data: analytics.trends.dailyRegistrations.map(item => item.count),
                      borderColor: 'rgba(102, 126, 234, 1)',
                      backgroundColor: 'rgba(102, 126, 234, 0.1)',
                      borderWidth: 3,
                      fill: true,
                      tension: 0.4,
                      pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                      pointBorderColor: '#fff',
                      pointBorderWidth: 2,
                      pointRadius: 6,
                      pointHoverRadius: 8,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                    title: {
                      display: false,
                    },
                  },
                  scales: {
                    x: {
                      grid: {
                        display: false,
                      },
                    },
                    y: {
                      beginAtZero: true,
                      ticks: {
                        stepSize: 1,
                      },
                      grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                      },
                    },
                  },
                  interaction: {
                    intersect: false,
                    mode: 'index',
                  },
                }}
              />
            </div>
          </Card>
        </div>
      )}
    </AdminLayoutNew>
  )
}
