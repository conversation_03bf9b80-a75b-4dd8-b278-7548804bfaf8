'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { getErrorMessage, parseApiError } from '@/lib/error-messages'
import {
  Settings,
  Shield,
  Users,
  Database,
  Bell,
  Mail,
  Lock,
  Globe,
  Palette,
  Download,
  Upload,
  Save,
  Check,
  AlertCircle,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'

interface SettingItem {
  id: string
  name: string
  value: string | boolean
  type: 'text' | 'email' | 'select' | 'toggle' | 'number'
  options?: string[]
  description?: string
}

interface SettingsState {
  [key: string]: SettingItem[]
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SettingsState>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [editingCategory, setEditingCategory] = useState<string | null>(null)
  const [userRole, setUserRole] = useState<string>('')
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { addToast } = useToast()

  useEffect(() => {
    loadSettings()
    getCurrentUser()
  }, [])

  const getCurrentUser = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUserRole(data.user?.role?.name || '')
      }
    } catch (error) {
      // Silently handle user fetch error - not critical for settings page
      addToast({
        type: 'warning',
        title: 'User Information Unavailable',
        description: 'Unable to load user information. Some features may be limited.',
        duration: 4000
      })
    }
  }

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings')

      if (!response.ok) {
        throw new Error('Failed to fetch settings')
      }

      const data = await response.json()

      if (data.success) {
        // Transform the API response to match the component's expected format
        const transformedSettings: SettingsState = {}

        Object.keys(data.settings).forEach(category => {
          transformedSettings[category] = data.settings[category].map((setting: any) => ({
            id: setting.key,
            name: setting.name,
            value: setting.value,
            type: setting.type,
            options: setting.options,
            description: setting.description
          }))
        })

        setSettings(transformedSettings)
      } else {
        throw new Error('Invalid response format')
      }
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Failed to Load Settings',
        description: 'Unable to load system settings from the server. This could be due to insufficient permissions or a server issue.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nTime: ${new Date().toISOString()}`,
        errorCode: 'SETTINGS_LOAD_ERROR'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateSetting = (category: string, settingId: string, newValue: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [category]: prev[category].map(setting =>
        setting.id === settingId ? { ...setting, value: newValue } : setting
      )
    }))
  }

  const saveSettings = async (category: string) => {
    setSaving(true)
    try {
      // Prepare settings data for API
      const categorySettings = settings[category] || []
      const settingsToSave = categorySettings.map(setting => ({
        category,
        key: setting.id,
        value: setting.value
      }))

      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: settingsToSave
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save settings')
      }

      const data = await response.json()

      if (data.success) {
        // Show success toast
        addToast({
          type: 'success',
          title: 'Settings Saved Successfully',
          description: `${getCategoryDisplayName(category)} settings have been saved and are now in effect across the system.`,
          duration: 5000
        })
        setEditingCategory(null)
        setMessage(null) // Clear any existing message
      } else {
        throw new Error('Invalid response format')
      }
    } catch (error) {
      const errorMessage = parseApiError(error)
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Failed to Save Settings',
        description: 'Unable to save the settings changes. This could be due to validation errors or insufficient permissions.',
        details: `Error: ${errorMessage}\nCategory: ${getCategoryDisplayName(category)}\nTime: ${new Date().toISOString()}`,
        errorCode: 'SETTINGS_SAVE_ERROR'
      })
    } finally {
      setSaving(false)
    }
  }

  const getCategoryDisplayName = (category: string) => {
    const categoryMap: Record<string, string> = {
      userManagement: 'User Management',
      security: 'Security',
      notifications: 'Notifications',
      system: 'System'
    }
    return categoryMap[category] || category
  }

  const settingsCategories = [
    {
      id: 'userManagement',
      title: 'User Management',
      description: 'Manage user roles and permissions',
      icon: Users,
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 'security',
      title: 'Security',
      description: 'Security and authentication settings',
      icon: Shield,
      color: 'from-red-500 to-pink-600',
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Email and system notifications',
      icon: Bell,
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 'system',
      title: 'System',
      description: 'General system configuration',
      icon: Database,
      color: 'from-purple-500 to-violet-600',
    }
  ]

  const renderSettingInput = (category: string, setting: SettingItem) => {
    const isEditing = editingCategory === category

    if (!isEditing) {
      return (
        <div className="flex items-center space-x-2">
          {setting.type === 'toggle' ? (
            <Badge
              className={`font-apercu-medium text-xs ${
                setting.value
                  ? 'bg-green-100 text-green-800 border-green-200'
                  : 'bg-gray-100 text-gray-800 border-gray-200'
              }`}
            >
              {setting.value ? 'Enabled' : 'Disabled'}
            </Badge>
          ) : (
            <Badge variant="secondary" className="font-apercu-medium text-xs">
              {setting.value.toString()}
            </Badge>
          )}
        </div>
      )
    }

    switch (setting.type) {
      case 'toggle':
        return (
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={setting.value as boolean}
              onChange={(e) => updateSetting(category, setting.id, e.target.checked)}
              className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <span className="font-apercu-medium text-sm text-gray-700">
              {setting.value ? 'Enabled' : 'Disabled'}
            </span>
          </label>
        )

      case 'select':
        return (
          <select
            value={setting.value as string}
            onChange={(e) => updateSetting(category, setting.id, e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            {setting.options?.map((option) => (
              <option key={option} value={option.toLowerCase()}>
                {option}
              </option>
            ))}
          </select>
        )

      case 'number':
        return (
          <input
            type="number"
            value={setting.value as string}
            onChange={(e) => updateSetting(category, setting.id, e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-24"
          />
        )

      default:
        return (
          <input
            type={setting.type}
            value={setting.value as string}
            onChange={(e) => updateSetting(category, setting.id, e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
        )
    }
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Settings" description="Configure system settings and preferences">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin']}>
      <AdminLayoutNew
        title="Settings"
        description="Configure system settings and preferences"
      >
      {/* Success/Error Message */}
      {message && (
        <div className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success'
            ? 'bg-green-50 border border-green-200'
            : 'bg-red-50 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <Check className="h-5 w-5 text-green-600" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-600" />
          )}
          <span className={`font-apercu-medium text-sm ${
            message.type === 'success' ? 'text-green-700' : 'text-red-700'
          }`}>
            {message.text}
          </span>
        </div>
      )}
      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
        <Card className="p-4 sm:p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Backup Data</p>
              <p className="font-apercu-regular text-xs text-gray-500 truncate">Export system data</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Download className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Import Data</p>
              <p className="font-apercu-regular text-xs text-gray-500 truncate">Import configurations</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Upload className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">System Logs</p>
              <p className="font-apercu-regular text-xs text-gray-500 truncate">View activity logs</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Database className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">API Keys</p>
              <p className="font-apercu-regular text-xs text-gray-500 truncate">Manage integrations</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Lock className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>
      </div>

      {/* Settings Categories */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        {settingsCategories.map((category) => {
          const categorySettings = settings[category.id] || []
          const isEditing = editingCategory === category.id

          return (
            <Card key={category.id} className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
                <div className="flex items-center">
                  <div className={`h-10 w-10 sm:h-12 sm:w-12 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0`}>
                    <category.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-apercu-bold text-base sm:text-lg text-gray-900 truncate">{category.title}</h3>
                    <p className="font-apercu-regular text-sm text-gray-600 truncate">{category.description}</p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                  {!isEditing && userRole === 'Super Admin' ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingCategory(category.id)}
                      className="font-apercu-medium w-full sm:w-auto"
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  ) : userRole === 'Super Admin' && isEditing ? (
                    <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingCategory(null)}
                        className="font-apercu-medium w-full sm:w-auto"
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => saveSettings(category.id)}
                        disabled={saving}
                        className="font-apercu-medium w-full sm:w-auto"
                      >
                        {saving ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            <span className="sm:hidden">Saving Changes...</span>
                            <span className="hidden sm:inline">Saving...</span>
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-1" />
                            Save
                          </>
                        )}
                      </Button>
                    </div>
                  ) : userRole === 'Admin' ? (
                    <Badge variant="secondary" className="font-apercu-medium text-xs w-full sm:w-auto justify-center">
                      <Eye className="h-3 w-3 mr-1" />
                      Read Only
                    </Badge>
                  ) : null}
                </div>
              </div>

              <div className="space-y-4">
                {categorySettings.map((setting) => (
                  <div key={setting.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-3 border-b border-gray-100 last:border-b-0 space-y-3 sm:space-y-0">
                    <div className="flex-1 min-w-0">
                      <p className="font-apercu-medium text-sm text-gray-900 truncate">{setting.name}</p>
                      {setting.description && (
                        <p className="font-apercu-regular text-xs text-gray-500 mt-1 break-words">{setting.description}</p>
                      )}
                    </div>
                    <div className="flex items-center justify-start sm:justify-end">
                      {renderSettingInput(category.id, setting)}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )
        })}
      </div>

      {/* System Information */}
      <Card className="p-4 sm:p-6 mt-6">
        <h3 className="font-apercu-bold text-base sm:text-lg text-gray-900 mb-4">System Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <div>
            <p className="font-apercu-medium text-sm text-gray-600 mb-1">Version</p>
            <p className="font-apercu-regular text-base sm:text-lg text-gray-900">v1.0.0</p>
          </div>
          <div>
            <p className="font-apercu-medium text-sm text-gray-600 mb-1">Last Updated</p>
            <p className="font-apercu-regular text-base sm:text-lg text-gray-900">Today</p>
          </div>
          <div className="sm:col-span-2 lg:col-span-1">
            <p className="font-apercu-medium text-sm text-gray-600 mb-1">Environment</p>
            <Badge className="bg-green-100 text-green-800 border-green-200 font-apercu-medium">
              Development
            </Badge>
          </div>
        </div>
      </Card>

      {/* Error Modal */}
      <ErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
        type={errorModal.type}
        title={errorModal.title}
        description={errorModal.description}
        details={errorModal.details}
        errorCode={errorModal.errorCode}
        showRetry={errorModal.type === 'error'}
        onRetry={() => {
          setErrorModal(prev => ({ ...prev, isOpen: false }))
          loadSettings()
        }}
        showContactSupport={errorModal.type === 'error'}
      />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
