'use client'

import { useEffect, useState } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { CreateUserModal } from '@/components/admin/CreateUserModal'
import { EditUserModal } from '@/components/admin/EditUserModalNew'
import { DeleteUserModal } from '@/components/admin/DeleteUserModal'
import { ChangePasswordModal } from '@/components/admin/ChangePasswordModal'
import { SimpleMessaging } from '@/components/admin/SimpleMessaging'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { EnhancedBadge, getRoleBadgeVariant, getStatusBadgeVariant } from '@/components/ui/enhanced-badge'
import { Avatar } from '@/components/ui/avatar'
import { useToast } from '@/components/ui/toast'
import { ErrorModal } from '@/components/ui/error-modal'
import { parseApiError } from '@/lib/error-messages'
import { useUser } from '@/contexts/UserContext'
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Shield,
  Eye,
  UserCheck,
  UserX,
  Crown,
  Settings,
  Search,
  Filter,
  Key,
  MessageSquare
} from 'lucide-react'

interface User {
  id: string
  email: string
  name: string
  isActive: boolean
  lastLogin: string | null
  createdAt: string
  role: {
    id: string
    name: string
    description: string
    isSystem: boolean
  }
}

interface Role {
  id: string
  name: string
  description: string
  isSystem: boolean
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('')

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false)
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { addToast } = useToast()
  const { currentUser } = useUser()

  useEffect(() => {
    fetchUsers()
    fetchRoles()
  }, [])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users || [])
      } else {
        const errorData = await response.json()
        setErrorModal({
          isOpen: true,
          type: 'error',
          title: 'Failed to Load Users',
          description: 'Unable to fetch user data from the server. This could be due to insufficient permissions or a server issue.',
          details: `Error: ${errorData.error || 'Unknown error'}\nStatus: ${response.status}\nTime: ${new Date().toISOString()}`,
          errorCode: `FETCH_USERS_${response.status}`
        })
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Network Error',
        description: 'Unable to connect to the server. Please check your internet connection and try again.',
        duration: 8000,
        action: {
          label: 'Retry',
          onClick: () => fetchUsers()
        }
      })
    }
  }

  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/admin/roles')
      if (response.ok) {
        const data = await response.json()
        setRoles(data.roles || [])
      } else {
        const errorData = await response.json()
        setErrorModal({
          isOpen: true,
          type: 'error',
          title: 'Failed to Load Roles',
          description: 'Unable to fetch role data from the server. This could affect user management functionality.',
          details: `Error: ${errorData.error || 'Unknown error'}\nStatus: ${response.status}\nTime: ${new Date().toISOString()}`,
          errorCode: `FETCH_ROLES_${response.status}`
        })
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Network Error Loading Roles',
        description: 'Unable to load user roles. Some functionality may be limited.',
        duration: 6000,
        action: {
          label: 'Retry',
          onClick: () => fetchRoles()
        }
      })
    } finally {
      setLoading(false)
    }
  }



  const getRoleIcon = (roleName: string) => {
    switch (roleName) {
      case 'Super Admin':
        return <Crown className="h-3 w-3" />
      case 'Admin':
        return <Shield className="h-3 w-3" />
      case 'Manager':
        return <Settings className="h-3 w-3" />
      case 'Staff':
        return <UserCheck className="h-3 w-3" />
      default:
        return <Eye className="h-3 w-3" />
    }
  }

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatLastLogin = (lastLogin: string | null): string => {
    if (!lastLogin) return 'Never'

    const date = new Date(lastLogin)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 48) return 'Yesterday'
    return formatDate(lastLogin)
  }

  // CRUD handlers
  const handleCreateUser = () => {
    setShowCreateModal(true)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setShowEditModal(true)
  }

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user)
    setShowDeleteModal(true)
  }

  const handleUserCreated = () => {
    fetchUsers()
  }

  const handleUserUpdated = () => {
    fetchUsers()
  }

  const handleUserDeleted = () => {
    fetchUsers()
  }

  const handleChangePassword = (user: User) => {
    setSelectedUser(user)
    setShowChangePasswordModal(true)
  }

  const handlePasswordChanged = () => {
    fetchUsers()
  }

  const handleSendMessage = (user: User) => {
    setSelectedUser(user)
    setShowMessageModal(true)
  }

  const closeModals = () => {
    setShowCreateModal(false)
    setShowEditModal(false)
    setShowDeleteModal(false)
    setShowChangePasswordModal(false)
    setShowMessageModal(false)
    setSelectedUser(null)
  }

  // Filter users based on search and role filter, excluding current user
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = filterRole === '' || user.role.id === filterRole
    const isNotCurrentUser = user.id !== currentUser?.id // Exclude current user
    return matchesSearch && matchesRole && isNotCurrentUser
  })

  if (loading) {
    return (
      <AdminLayoutNew title="User Management" description="Manage admin users and permissions">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager']}>
      <AdminLayoutNew title="User Management" description="Manage admin users and permissions">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Total Users</p>
              <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{users.length}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Users className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Active Users</p>
              <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">
                {users.filter(u => u.isActive).length}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <UserCheck className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Roles</p>
              <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{roles.length}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>

        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">System Roles</p>
              <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">
                {roles.filter(r => r.isSystem).length}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
              <Crown className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-4 sm:p-6 mb-6">
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1 sm:max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                  className="w-full pl-10 pr-8 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                >
                  <option value="">All Roles</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>

              <Button onClick={handleCreateUser} className="font-apercu-medium w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="text-white">Add User</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Results count */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="font-apercu-regular text-sm text-gray-600">
            Showing {filteredUsers.length} of {users.length} users
            {searchTerm && (
              <span className="ml-2">
                • Filtered by: <span className="font-apercu-medium">"{searchTerm}"</span>
              </span>
            )}
            {filterRole && (
              <span className="ml-2">
                • Role: <span className="font-apercu-medium">{roles.find(r => r.id === filterRole)?.name}</span>
              </span>
            )}
          </p>
        </div>
      </Card>

      {/* Users Container with Tabs */}
      <div className="mt-6">
        <Card className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div className="mb-4 sm:mb-0">
              <h3 className="font-apercu-bold text-lg text-gray-900">System Users</h3>
              <p className="font-apercu-regular text-sm text-gray-600">Manage admin users and their permissions</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-white" />
              </div>
              <span className="font-apercu-medium text-sm text-gray-700">
                {filteredUsers.length} users
              </span>
            </div>
          </div>

        {/* Desktop Table */}
        <div className="hidden lg:block">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[800px]">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[200px]">User</th>
                  <th className="text-left py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[120px]">Role</th>
                  <th className="text-left py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[100px]">Status</th>
                  <th className="text-left py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[120px]">Last Login</th>
                  <th className="text-left py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[100px]">Created</th>
                  <th className="text-right py-3 px-4 font-apercu-medium text-sm text-gray-600 min-w-[160px]">Actions</th>
                </tr>
              </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white font-apercu-bold text-sm">
                          {getInitials(user.name)}
                        </span>
                      </Avatar>
                      <div>
                        <p className="font-apercu-medium text-sm text-gray-900">{user.name}</p>
                        <p className="font-apercu-regular text-xs text-gray-500">{user.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <EnhancedBadge
                      variant={getRoleBadgeVariant(user.role.name, user.role.isSystem)}
                      className="font-apercu-medium"
                      icon={getRoleIcon(user.role.name)}
                    >
                      {user.role.name}
                    </EnhancedBadge>
                  </td>
                  <td className="py-4 px-4">
                    <EnhancedBadge
                      variant={getStatusBadgeVariant(user.isActive ? 'active' : 'inactive')}
                      className="font-apercu-medium"
                      icon={user.isActive ? <UserCheck className="h-3 w-3" /> : <UserX className="h-3 w-3" />}
                    >
                      {user.isActive ? 'Active' : 'Inactive'}
                    </EnhancedBadge>
                  </td>
                  <td className="py-4 px-4">
                    <span className="font-apercu-regular text-sm text-gray-600">
                      {formatLastLogin(user.lastLogin)}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className="font-apercu-regular text-sm text-gray-600">
                      {formatDate(user.createdAt)}
                    </span>
                  </td>
                  <td className="py-4 px-4 text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="font-apercu-medium"
                        onClick={() => handleEditUser(user)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="font-apercu-medium text-blue-600 hover:text-blue-700 hover:border-blue-300"
                        onClick={() => handleSendMessage(user)}
                      >
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="font-apercu-medium text-orange-600 hover:text-orange-700 hover:border-orange-300"
                        onClick={() => handleChangePassword(user)}
                      >
                        <Key className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="font-apercu-medium text-red-600 hover:text-red-700 hover:border-red-300"
                        onClick={() => handleDeleteUser(user)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden space-y-4">
          {filteredUsers.map((user) => (
            <Card key={user.id} className="p-4">
              {/* User Info Header */}
              <div className="flex items-center space-x-3 mb-4">
                <Avatar className="h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-apercu-bold text-sm">
                    {getInitials(user.name)}
                  </span>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="font-apercu-medium text-base text-gray-900 truncate">{user.name}</p>
                  <p className="font-apercu-regular text-sm text-gray-500 truncate">{user.email}</p>
                </div>
              </div>

              {/* Action Buttons - Full Width Row */}
              <div className="grid grid-cols-4 gap-2 mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="font-apercu-medium flex flex-col items-center py-3 h-auto"
                  onClick={() => handleEditUser(user)}
                >
                  <Edit className="h-4 w-4 mb-1" />
                  <span className="text-xs">Edit</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="font-apercu-medium text-blue-600 hover:text-blue-700 hover:border-blue-300 flex flex-col items-center py-3 h-auto"
                  onClick={() => handleSendMessage(user)}
                >
                  <MessageSquare className="h-4 w-4 mb-1" />
                  <span className="text-xs">Message</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="font-apercu-medium text-orange-600 hover:text-orange-700 hover:border-orange-300 flex flex-col items-center py-3 h-auto"
                  onClick={() => handleChangePassword(user)}
                >
                  <Key className="h-4 w-4 mb-1" />
                  <span className="text-xs">Password</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="font-apercu-medium text-red-600 hover:text-red-700 hover:border-red-300 flex flex-col items-center py-3 h-auto"
                  onClick={() => handleDeleteUser(user)}
                >
                  <Trash2 className="h-4 w-4 mb-1" />
                  <span className="text-xs">Delete</span>
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <p className="font-apercu-medium text-gray-600 mb-1">Role</p>
                  <EnhancedBadge
                    variant={getRoleBadgeVariant(user.role.name, user.role.isSystem)}
                    className="font-apercu-medium text-xs"
                    icon={getRoleIcon(user.role.name)}
                  >
                    {user.role.name}
                  </EnhancedBadge>
                </div>
                <div>
                  <p className="font-apercu-medium text-gray-600 mb-1">Status</p>
                  <EnhancedBadge
                    variant={getStatusBadgeVariant(user.isActive ? 'active' : 'inactive')}
                    className="font-apercu-medium text-xs"
                    icon={user.isActive ? <UserCheck className="h-3 w-3" /> : <UserX className="h-3 w-3" />}
                  >
                    {user.isActive ? 'Active' : 'Inactive'}
                  </EnhancedBadge>
                </div>
                <div>
                  <p className="font-apercu-medium text-gray-600 mb-1">Last Login</p>
                  <p className="font-apercu-regular text-gray-900">{formatLastLogin(user.lastLogin)}</p>
                </div>
                <div>
                  <p className="font-apercu-medium text-gray-600 mb-1">Created</p>
                  <p className="font-apercu-regular text-gray-900">{formatDate(user.createdAt)}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="font-apercu-medium text-gray-500">
              {users.length === 0 ? 'No users found' : 'No users match your search'}
            </p>
            <p className="font-apercu-regular text-sm text-gray-400">
              {users.length === 0
                ? 'Create your first user to get started'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
            {users.length === 0 && (
              <Button onClick={handleCreateUser} className="mt-4 font-apercu-medium">
                <Plus className="h-4 w-4 mr-2" />
                Create First User
              </Button>
            )}
          </div>
        )}
        </Card>
      </div>

      {/* Modals */}
      <CreateUserModal
        isOpen={showCreateModal}
        onClose={closeModals}
        onUserCreated={handleUserCreated}
        roles={roles}
      />

      <EditUserModal
        isOpen={showEditModal}
        onClose={closeModals}
        onUserUpdated={handleUserUpdated}
        user={selectedUser}
        roles={roles}
      />

      <DeleteUserModal
        isOpen={showDeleteModal}
        onClose={closeModals}
        onUserDeleted={handleUserDeleted}
        user={selectedUser}
      />

      <ChangePasswordModal
        isOpen={showChangePasswordModal}
        onClose={closeModals}
        onPasswordChanged={handlePasswordChanged}
        user={selectedUser}
      />

      <SimpleMessaging
        isOpen={showMessageModal}
        onClose={closeModals}
        recipient={selectedUser ? {
          id: selectedUser.id,
          name: selectedUser.name,
          email: selectedUser.email,
          role: selectedUser.role,
          type: 'admin' as const
        } : null}
      />

      {/* Error Modal */}
      <ErrorModal
        isOpen={errorModal.isOpen}
        onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
        type={errorModal.type}
        title={errorModal.title}
        description={errorModal.description}
        details={errorModal.details}
        errorCode={errorModal.errorCode}
        showRetry={errorModal.type === 'error'}
        onRetry={() => {
          setErrorModal(prev => ({ ...prev, isOpen: false }))
          fetchUsers()
          fetchRoles()
        }}
        showContactSupport={errorModal.type === 'error'}
      />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
