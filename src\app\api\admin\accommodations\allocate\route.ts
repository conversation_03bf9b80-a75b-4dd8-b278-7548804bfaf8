import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to allocate rooms
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const data = await request.json()
    const { ageRangeYears, allocateAll = false } = data

    // Validate age range
    if (!ageRangeYears || typeof ageRangeYears !== 'number' || ageRangeYears < 1) {
      return NextResponse.json(
        { error: 'Age range in years is required and must be a positive number' },
        { status: 400 }
      )
    }

    // Get all unallocated registrations
    const unallocatedRegistrations = await prisma.registration.findMany({
      where: {
        roomAllocation: null
      },
      orderBy: [
        { gender: 'asc' },
        { dateOfBirth: 'desc' } // Older participants first
      ]
    })

    if (unallocatedRegistrations.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unallocated registrations found',
        allocations: []
      })
    }

    // Get all active rooms
    const rooms = await prisma.room.findMany({
      where: { isActive: true },
      include: {
        allocations: true
      },
      orderBy: { name: 'asc' }
    })

    if (rooms.length === 0) {
      return NextResponse.json(
        { error: 'No active rooms available for allocation' },
        { status: 400 }
      )
    }

    // Calculate age for each registration
    const registrationsWithAge = unallocatedRegistrations.map(reg => {
      const today = new Date()
      const birthDate = new Date(reg.dateOfBirth)
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return { ...reg, age }
    })

    // Group registrations by gender with strict age difference validation
    const groupedRegistrations: Record<string, typeof registrationsWithAge> = {}
    let groupCounter = 0

    // Sort registrations by gender and age for better grouping
    const sortedRegistrations = registrationsWithAge.sort((a, b) => {
      if (a.gender !== b.gender) {
        return a.gender.localeCompare(b.gender)
      }
      return a.age - b.age
    })

    // Process each registration and assign to appropriate group
    for (const registration of sortedRegistrations) {
      let assignedToGroup = false

      // Try to find an existing group that can accommodate this registration
      for (const [groupKey, groupMembers] of Object.entries(groupedRegistrations)) {
        // Only consider groups of the same gender
        if (!groupKey.startsWith(registration.gender)) continue

        // Check if adding this registration would violate age difference constraint
        const existingAges = groupMembers.map(r => r.age)
        const allAges = [...existingAges, registration.age]
        const minAge = Math.min(...allAges)
        const maxAge = Math.max(...allAges)
        const ageDifference = maxAge - minAge

        // If age difference is within limit, add to this group
        if (ageDifference <= ageRangeYears) {
          groupedRegistrations[groupKey].push(registration)
          assignedToGroup = true
          break
        }
      }

      // If no suitable group found, create a new group
      if (!assignedToGroup) {
        groupCounter++
        const newGroupKey = `${registration.gender}-Group${groupCounter}`
        groupedRegistrations[newGroupKey] = [registration]
      }
    }

    // Helper function to get age range for a group
    function getGroupAgeRange(groupMembers: typeof registrationsWithAge): { min: number, max: number } {
      const ages = groupMembers.map(r => r.age)
      return {
        min: Math.min(...ages),
        max: Math.max(...ages)
      }
    }

    const allocations = []
    const allocationResults = []

    // Process each group
    for (const [groupKey, groupRegistrations] of Object.entries(groupedRegistrations)) {
      const [gender] = groupKey.split('-')
      const ageRange = getGroupAgeRange(groupRegistrations)
      const ageDifference = ageRange.max - ageRange.min

      // Validate that the group respects the age difference constraint
      if (ageDifference > ageRangeYears) {
        allocationResults.push({
          group: `${gender} (${ageRange.min}-${ageRange.max} years) - Age difference: ${ageDifference} years`,
          count: groupRegistrations.length,
          status: 'failed',
          reason: `Age difference of ${ageDifference} years exceeds limit of ${ageRangeYears} years`
        })
        continue
      }

      // Find suitable rooms for this group
      const suitableRooms = rooms.filter(room =>
        room.gender === gender &&
        room.allocations.length < room.capacity
      ).sort((a, b) => {
        // Prioritize rooms with more available space
        const aAvailable = a.capacity - a.allocations.length
        const bAvailable = b.capacity - b.allocations.length
        return bAvailable - aAvailable
      })

      if (suitableRooms.length === 0) {
        allocationResults.push({
          group: `${gender} (${ageRange.min}-${ageRange.max} years) - Age difference: ${ageDifference} years`,
          count: groupRegistrations.length,
          status: 'failed',
          reason: `No suitable ${gender.toLowerCase()} rooms available`
        })
        continue
      }

      // Allocate registrations to rooms with additional age validation
      let registrationIndex = 0
      const roomAllocations: Array<{ roomId: string, registrations: typeof registrationsWithAge }> = []

      for (const room of suitableRooms) {
        const availableSpaces = room.capacity - room.allocations.length
        const toAllocate = Math.min(availableSpaces, groupRegistrations.length - registrationIndex)

        if (toAllocate === 0) continue

        // Get registrations to allocate to this room
        const roomRegistrations = groupRegistrations.slice(registrationIndex, registrationIndex + toAllocate)

        // Validate age difference within this room allocation
        const roomAges = roomRegistrations.map(r => r.age)
        const roomMinAge = Math.min(...roomAges)
        const roomMaxAge = Math.max(...roomAges)
        const roomAgeDifference = roomMaxAge - roomMinAge

        if (roomAgeDifference > ageRangeYears) {
          // Skip this room allocation if it would violate age constraints
          continue
        }

        // Create allocations for this room
        for (let i = 0; i < toAllocate; i++) {
          const registration = groupRegistrations[registrationIndex + i]
          allocations.push({
            registrationId: registration.id,
            roomId: room.id,
            allocatedBy: currentUser.email
          })
        }

        roomAllocations.push({
          roomId: room.id,
          registrations: roomRegistrations
        })

        // Update room allocations count for next iteration
        for (let i = 0; i < toAllocate; i++) {
          room.allocations.push({ id: 'temp' } as any)
        }

        registrationIndex += toAllocate

        if (registrationIndex >= groupRegistrations.length) {
          break
        }
      }

      const allocated = registrationIndex
      const remaining = groupRegistrations.length - allocated

      allocationResults.push({
        group: `${gender} (${ageRange.min}-${ageRange.max} years) - Age difference: ${ageDifference} years`,
        count: groupRegistrations.length,
        allocated,
        remaining,
        status: remaining === 0 ? 'success' : 'partial',
        roomAllocations: roomAllocations.map(ra => ({
          roomId: ra.roomId,
          count: ra.registrations.length,
          ageRange: `${Math.min(...ra.registrations.map(r => r.age))}-${Math.max(...ra.registrations.map(r => r.age))} years`
        }))
      })
    }

    // Create allocations in database
    if (allocations.length > 0) {
      await prisma.roomAllocation.createMany({
        data: allocations
      })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully allocated ${allocations.length} registrations with maximum ${ageRangeYears}-year age difference per room`,
      totalProcessed: unallocatedRegistrations.length,
      totalAllocated: allocations.length,
      allocationResults,
      ageRangeYears,
      summary: {
        totalGroups: Object.keys(groupedRegistrations).length,
        successfulAllocations: allocationResults.filter(r => r.status === 'success').length,
        partialAllocations: allocationResults.filter(r => r.status === 'partial').length,
        failedAllocations: allocationResults.filter(r => r.status === 'failed').length
      }
    })

  } catch (error) {
    console.error('Error allocating rooms:', error)
    return NextResponse.json(
      { error: 'Failed to allocate rooms' },
      { status: 500 }
    )
  }
}
