import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { authenticateRequest } from '@/lib/auth-helpers'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to allocate rooms
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const data = await request.json()
    const { ageRangeYears, allocateAll = false } = data

    // Validate age range
    if (!ageRangeYears || typeof ageRangeYears !== 'number' || ageRangeYears < 1) {
      return NextResponse.json(
        { error: 'Age range in years is required and must be a positive number' },
        { status: 400 }
      )
    }

    // Get all unallocated registrations
    const unallocatedRegistrations = await prisma.registration.findMany({
      where: {
        roomAllocation: null
      },
      orderBy: [
        { gender: 'asc' },
        { dateOfBirth: 'desc' } // Older participants first
      ]
    })

    if (unallocatedRegistrations.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unallocated registrations found',
        allocations: []
      })
    }

    // Get all active rooms
    const rooms = await prisma.room.findMany({
      where: { isActive: true },
      include: {
        allocations: true
      },
      orderBy: { name: 'asc' }
    })

    if (rooms.length === 0) {
      return NextResponse.json(
        { error: 'No active rooms available for allocation' },
        { status: 400 }
      )
    }

    // Calculate age for each registration
    const registrationsWithAge = unallocatedRegistrations.map(reg => {
      const today = new Date()
      const birthDate = new Date(reg.dateOfBirth)
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return { ...reg, age }
    })

    // Group registrations by gender and age groups with age difference validation
    const groupedRegistrations = registrationsWithAge.reduce((groups, reg) => {
      const ageGroup = Math.floor(reg.age / ageRangeYears) * ageRangeYears
      const key = `${reg.gender}-${ageGroup}`

      if (!groups[key]) {
        groups[key] = []
      }

      // Validate age difference within the group
      const existingAges = groups[key].map(r => r.age)
      if (existingAges.length > 0) {
        const minAge = Math.min(...existingAges)
        const maxAge = Math.max(...existingAges)
        const currentAgeDiff = Math.max(maxAge, reg.age) - Math.min(minAge, reg.age)

        // If adding this registration would exceed the age range, skip it for now
        if (currentAgeDiff > ageRangeYears) {
          // Try to find or create a more suitable group
          const suitableGroupKey = findSuitableAgeGroup(reg, groups, ageRangeYears)
          if (suitableGroupKey) {
            groups[suitableGroupKey].push(reg)
          } else {
            // Create a new group for this registration
            const newGroupKey = `${reg.gender}-${reg.age}`
            if (!groups[newGroupKey]) {
              groups[newGroupKey] = []
            }
            groups[newGroupKey].push(reg)
          }
          return groups
        }
      }

      groups[key].push(reg)
      return groups
    }, {} as Record<string, typeof registrationsWithAge>)

    // Helper function to find suitable age group
    function findSuitableAgeGroup(registration: typeof registrationsWithAge[0], groups: Record<string, typeof registrationsWithAge>, maxAgeDiff: number): string | null {
      for (const [groupKey, groupMembers] of Object.entries(groups)) {
        if (!groupKey.startsWith(registration.gender)) continue

        const ages = groupMembers.map(r => r.age)
        const minAge = Math.min(...ages)
        const maxAge = Math.max(...ages)
        const wouldBeMinAge = Math.min(minAge, registration.age)
        const wouldBeMaxAge = Math.max(maxAge, registration.age)

        if ((wouldBeMaxAge - wouldBeMinAge) <= maxAgeDiff) {
          return groupKey
        }
      }
      return null
    }

    const allocations = []
    const allocationResults = []

    // Process each group
    for (const [groupKey, groupRegistrations] of Object.entries(groupedRegistrations)) {
      const [gender, ageGroupStr] = groupKey.split('-')
      const ageGroup = parseInt(ageGroupStr)

      // Find suitable rooms for this group
      const suitableRooms = rooms.filter(room => 
        room.gender === gender && 
        room.allocations.length < room.capacity
      ).sort((a, b) => {
        // Prioritize rooms with more available space
        const aAvailable = a.capacity - a.allocations.length
        const bAvailable = b.capacity - b.allocations.length
        return bAvailable - aAvailable
      })

      if (suitableRooms.length === 0) {
        allocationResults.push({
          group: `${gender} (${ageGroup}-${ageGroup + ageRangeYears - 1} years)`,
          count: groupRegistrations.length,
          status: 'failed',
          reason: `No suitable ${gender.toLowerCase()} rooms available`
        })
        continue
      }

      // Allocate registrations to rooms
      let registrationIndex = 0
      for (const room of suitableRooms) {
        const availableSpaces = room.capacity - room.allocations.length
        const toAllocate = Math.min(availableSpaces, groupRegistrations.length - registrationIndex)

        for (let i = 0; i < toAllocate; i++) {
          const registration = groupRegistrations[registrationIndex + i]
          allocations.push({
            registrationId: registration.id,
            roomId: room.id,
            allocatedBy: currentUser.email
          })
          
          // Update room allocations count for next iteration
          room.allocations.push({ id: 'temp' } as any)
        }

        registrationIndex += toAllocate

        if (registrationIndex >= groupRegistrations.length) {
          break
        }
      }

      const allocated = registrationIndex
      const remaining = groupRegistrations.length - allocated

      allocationResults.push({
        group: `${gender} (${ageGroup}-${ageGroup + ageRangeYears - 1} years)`,
        count: groupRegistrations.length,
        allocated,
        remaining,
        status: remaining === 0 ? 'success' : 'partial'
      })
    }

    // Create allocations in database
    if (allocations.length > 0) {
      await prisma.roomAllocation.createMany({
        data: allocations
      })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully allocated ${allocations.length} registrations`,
      totalProcessed: unallocatedRegistrations.length,
      totalAllocated: allocations.length,
      allocationResults,
      ageRangeYears
    })

  } catch (error) {
    console.error('Error allocating rooms:', error)
    return NextResponse.json(
      { error: 'Failed to allocate rooms' },
      { status: 500 }
    )
  }
}
