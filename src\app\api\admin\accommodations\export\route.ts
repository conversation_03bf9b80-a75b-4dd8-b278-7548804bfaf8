import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { authenticateRequest } from '@/lib/auth-helpers'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to export accommodations (Staff and Viewer cannot export)
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'csv' // 'csv' or 'pdf'
    const search = searchParams.get('search') || ''
    const filter = searchParams.get('filter') || 'all'

    // Build search conditions (same as search route)
    const searchConditions = []

    if (search.trim()) {
      const searchTerm = search.trim()
      searchConditions.push({
        OR: [
          { fullName: { contains: searchTerm } },
          { emailAddress: { contains: searchTerm } },
          { phoneNumber: { contains: searchTerm } },
          {
            roomAllocation: {
              room: {
                name: { contains: searchTerm }
              }
            }
          }
        ]
      })
    }

    if (filter === 'allocated') {
      searchConditions.push({
        roomAllocation: {
          isNot: null
        }
      })
    } else if (filter === 'unallocated') {
      searchConditions.push({
        roomAllocation: null
      })
    }

    const whereClause = searchConditions.length > 0 ? { AND: searchConditions } : {}

    // Get data for export
    const registrations = await prisma.registration.findMany({
      where: whereClause,
      include: {
        roomAllocation: {
          include: {
            room: {
              select: {
                id: true,
                name: true,
                gender: true,
                capacity: true,
                description: true
              }
            }
          }
        }
      },
      orderBy: [
        { roomAllocation: { room: { name: 'asc' } } },
        { fullName: 'asc' }
      ]
    })

    if (format === 'csv') {
      return generateCSV(registrations)
    } else if (format === 'pdf') {
      return generatePDF(registrations)
    } else {
      return NextResponse.json({ error: 'Invalid format' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error exporting accommodations:', error)
    return NextResponse.json(
      { error: 'Failed to export accommodations' },
      { status: 500 }
    )
  }
}

function generateCSV(registrations: any[]) {
  const headers = [
    'Full Name',
    'Gender',
    'Age',
    'Email',
    'Phone',
    'Address',
    'Room Name',
    'Room Gender',
    'Room Capacity',
    'Emergency Contact Name',
    'Emergency Contact Phone',
    'Emergency Contact Relationship',
    'Parent/Guardian Name',
    'Parent/Guardian Phone',
    'Medications',
    'Allergies',
    'Special Needs',
    'Dietary Restrictions',
    'Registration Date'
  ]

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const csvRows = [
    headers.join(','),
    ...registrations.map(reg => [
      `"${reg.fullName}"`,
      `"${reg.gender}"`,
      calculateAge(reg.dateOfBirth),
      `"${reg.emailAddress}"`,
      `"${reg.phoneNumber}"`,
      `"${reg.address}"`,
      `"${reg.roomAllocation?.room?.name || 'Unallocated'}"`,
      `"${reg.roomAllocation?.room?.gender || 'N/A'}"`,
      reg.roomAllocation?.room?.capacity || 'N/A',
      `"${reg.emergencyContactName}"`,
      `"${reg.emergencyContactPhone}"`,
      `"${reg.emergencyContactRelationship}"`,
      `"${reg.parentGuardianName || ''}"`,
      `"${reg.parentGuardianPhone || ''}"`,
      `"${reg.medications || ''}"`,
      `"${reg.allergies || ''}"`,
      `"${reg.specialNeeds || ''}"`,
      `"${reg.dietaryRestrictions || ''}"`,
      `"${new Date(reg.createdAt).toLocaleDateString()}"`
    ].join(','))
  ]

  const csvContent = csvRows.join('\n')

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="accommodation-report-${new Date().toISOString().split('T')[0]}.csv"`
    }
  })
}

function generatePDF(registrations: any[]) {
  // For now, return a simple text-based PDF content
  // In a real implementation, you'd use a library like jsPDF or Puppeteer

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  // Group by rooms
  const roomGroups = registrations.reduce((groups, reg) => {
    const roomName = reg.roomAllocation?.room?.name || 'Unallocated'
    if (!groups[roomName]) {
      groups[roomName] = []
    }
    groups[roomName].push(reg)
    return groups
  }, {} as Record<string, any[]>)

  let pdfContent = `ACCOMMODATION REPORT
Generated on: ${new Date().toLocaleDateString()}
Total Registrations: ${registrations.length}

`

  Object.entries(roomGroups).forEach(([roomName, roomRegistrations]) => {
    const room = roomRegistrations[0]?.roomAllocation?.room

    pdfContent += `\n${roomName.toUpperCase()}\n`
    pdfContent += `${'='.repeat(roomName.length)}\n`

    if (room) {
      pdfContent += `Gender: ${room.gender} | Capacity: ${room.capacity} | Occupancy: ${roomRegistrations.length}\n`
    }

    pdfContent += `\n`

    roomRegistrations.forEach((reg, index) => {
      pdfContent += `${index + 1}. ${reg.fullName}\n`
      pdfContent += `   Age: ${calculateAge(reg.dateOfBirth)} | Gender: ${reg.gender}\n`
      pdfContent += `   Email: ${reg.emailAddress}\n`
      pdfContent += `   Phone: ${reg.phoneNumber}\n`
      pdfContent += `   Emergency Contact: ${reg.emergencyContactName} (${reg.emergencyContactPhone})\n`

      if (reg.medications || reg.allergies || reg.specialNeeds) {
        pdfContent += `   Medical Notes: `
        const medicalNotes = []
        if (reg.medications) medicalNotes.push(`Medications: ${reg.medications}`)
        if (reg.allergies) medicalNotes.push(`Allergies: ${reg.allergies}`)
        if (reg.specialNeeds) medicalNotes.push(`Special Needs: ${reg.specialNeeds}`)
        pdfContent += medicalNotes.join(' | ') + '\n'
      }

      pdfContent += `\n`
    })

    pdfContent += `\n`
  })

  return new NextResponse(pdfContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="accommodation-report-${new Date().toISOString().split('T')[0]}.pdf"`
    }
  })
}
