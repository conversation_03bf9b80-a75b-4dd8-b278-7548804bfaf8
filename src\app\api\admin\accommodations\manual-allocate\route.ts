import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to allocate rooms
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const data = await request.json()
    const { registrationId, roomId, maxAgeDifference = 5 } = data

    // Validate required fields
    if (!registrationId || !roomId) {
      return NextResponse.json(
        { error: 'Registration ID and Room ID are required' },
        { status: 400 }
      )
    }

    // Get registration details
    const registration = await prisma.registration.findUnique({
      where: { id: registrationId },
      include: { roomAllocation: true }
    })

    if (!registration) {
      return NextResponse.json(
        { error: 'Registration not found' },
        { status: 404 }
      )
    }

    if (registration.roomAllocation) {
      return NextResponse.json(
        { error: 'Registration is already allocated to a room' },
        { status: 400 }
      )
    }

    // Get room details with current allocations
    const room = await prisma.room.findUnique({
      where: { id: roomId },
      include: {
        allocations: {
          include: {
            registration: {
              select: {
                id: true,
                fullName: true,
                dateOfBirth: true,
                gender: true
              }
            }
          }
        }
      }
    })

    if (!room) {
      return NextResponse.json(
        { error: 'Room not found' },
        { status: 404 }
      )
    }

    if (!room.isActive) {
      return NextResponse.json(
        { error: 'Room is not active' },
        { status: 400 }
      )
    }

    // Check room capacity
    if (room.allocations.length >= room.capacity) {
      return NextResponse.json(
        { error: 'Room is at full capacity' },
        { status: 400 }
      )
    }

    // Check gender compatibility
    if (room.gender !== registration.gender) {
      return NextResponse.json(
        { error: `Room is designated for ${room.gender} participants only` },
        { status: 400 }
      )
    }

    // Calculate age of the new registration
    const calculateAge = (dateOfBirth: string): number => {
      const today = new Date()
      const birthDate = new Date(dateOfBirth)
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return age
    }

    const newAge = calculateAge(registration.dateOfBirth)

    // Check age compatibility with existing allocations
    if (room.allocations.length > 0) {
      const existingAges = room.allocations.map(allocation => 
        calculateAge(allocation.registration.dateOfBirth)
      )
      
      const allAges = [...existingAges, newAge]
      const minAge = Math.min(...allAges)
      const maxAge = Math.max(...allAges)
      const ageDifference = maxAge - minAge

      if (ageDifference > maxAgeDifference) {
        return NextResponse.json(
          { 
            error: `Age difference would be ${ageDifference} years, which exceeds the maximum allowed difference of ${maxAgeDifference} years`,
            details: {
              newParticipantAge: newAge,
              existingAgeRange: `${Math.min(...existingAges)}-${Math.max(...existingAges)} years`,
              maxAllowedDifference: maxAgeDifference
            }
          },
          { status: 400 }
        )
      }
    }

    // Create the allocation
    const allocation = await prisma.roomAllocation.create({
      data: {
        registrationId: registrationId,
        roomId: roomId,
        allocatedBy: currentUser.email,
        allocatedAt: new Date()
      },
      include: {
        registration: {
          select: {
            id: true,
            fullName: true,
            gender: true,
            dateOfBirth: true
          }
        },
        room: {
          select: {
            id: true,
            name: true,
            gender: true,
            capacity: true
          }
        }
      }
    })

    // Get updated room information
    const updatedRoom = await prisma.room.findUnique({
      where: { id: roomId },
      include: {
        allocations: {
          include: {
            registration: {
              select: {
                id: true,
                fullName: true,
                dateOfBirth: true,
                gender: true
              }
            }
          }
        }
      }
    })

    // Calculate age range for the room
    const roomAges = updatedRoom!.allocations.map(alloc => 
      calculateAge(alloc.registration.dateOfBirth)
    )
    const roomMinAge = Math.min(...roomAges)
    const roomMaxAge = Math.max(...roomAges)

    return NextResponse.json({
      success: true,
      message: `Successfully allocated ${registration.fullName} to ${room.name}`,
      allocation: {
        id: allocation.id,
        participant: {
          name: registration.fullName,
          age: newAge,
          gender: registration.gender
        },
        room: {
          name: room.name,
          currentOccupancy: updatedRoom!.allocations.length,
          capacity: room.capacity,
          ageRange: roomAges.length > 1 ? `${roomMinAge}-${roomMaxAge} years` : `${newAge} years`
        }
      }
    })

  } catch (error) {
    console.error('Error in manual allocation:', error)
    return NextResponse.json(
      { error: 'Failed to allocate participant to room' },
      { status: 500 }
    )
  }
}
