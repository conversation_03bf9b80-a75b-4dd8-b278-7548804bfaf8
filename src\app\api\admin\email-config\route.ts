import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    // Get email configuration from environment variables
    const emailConfig = {
      fromName: process.env.EMAIL_FROM_NAME || 'Youth Registration System',
      fromEmail: process.env.SMTP_USER || '<EMAIL>',
      replyTo: process.env.EMAIL_REPLY_TO || process.env.SMTP_USER || '<EMAIL>',
      smtpHost: process.env.SMTP_HOST || 'smtp.gmail.com',
      smtpPort: process.env.SMTP_PORT || '587',
      isSecure: process.env.SMTP_SECURE === 'true' || process.env.SMTP_PORT === '465',
      isConfigured: !!(process.env.SMTP_USER && process.env.SMTP_PASS),
      environment: process.env.NODE_ENV || 'development',
      adminEmails: process.env.ADMIN_EMAILS ? process.env.ADMIN_EMAILS.split(',') : ['<EMAIL>'],
      maxRecipientsPerEmail: parseInt(process.env.MAX_RECIPIENTS_PER_EMAIL || '50')
    }

    return NextResponse.json({
      config: emailConfig,
      status: emailConfig.isConfigured ? 'configured' : 'not_configured',
      message: emailConfig.isConfigured 
        ? 'Email system is properly configured and ready to send emails'
        : 'Email system requires SMTP configuration to send emails'
    })

  } catch (error) {
    console.error('Error fetching email config:', error)
    return NextResponse.json(
      { error: 'Failed to fetch email configuration' },
      { status: 500 }
    )
  }
}
