import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'
import { sendEmail } from '@/lib/email'
import { generateMessageEmail } from '@/lib/email-templates'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!
    const messageId = params.id
    const body = await request.json()
    const { content } = body

    // Validate required fields
    if (!content) {
      return NextResponse.json(
        { error: 'Reply content is required' },
        { status: 400 }
      )
    }

    // Get the original message
    const originalMessage = await prisma.message.findUnique({
      where: { id: messageId }
    })

    if (!originalMessage) {
      return NextResponse.json(
        { error: 'Original message not found' },
        { status: 404 }
      )
    }

    // Verify user has permission to reply (must be sender or recipient of original message)
    if (originalMessage.senderEmail !== currentUser.email && originalMessage.recipientEmail !== currentUser.email) {
      return NextResponse.json(
        { error: 'You can only reply to messages you sent or received' },
        { status: 403 }
      )
    }

    // Determine recipient (reply to sender if current user is recipient, or to recipient if current user is sender)
    const recipientEmail = originalMessage.senderEmail === currentUser.email 
      ? originalMessage.recipientEmail 
      : originalMessage.senderEmail
    const recipientName = originalMessage.senderEmail === currentUser.email 
      ? originalMessage.recipientName 
      : originalMessage.senderName

    // Get recipient details for type determination
    let recipientType = 'admin'
    const adminRecipient = await prisma.admin.findUnique({
      where: { email: recipientEmail }
    })
    if (!adminRecipient) {
      const userRecipient = await prisma.user.findUnique({
        where: { email: recipientEmail }
      })
      if (userRecipient) {
        recipientType = 'user'
      }
    }

    // Use existing threadId or create new one
    const threadId = originalMessage.threadId || originalMessage.id

    // Create reply message
    const replyMessage = await prisma.message.create({
      data: {
        subject: `Re: ${originalMessage.subject.startsWith('Re: ') ? originalMessage.subject.substring(4) : originalMessage.subject}`,
        content,
        senderEmail: currentUser.email,
        senderName: currentUser.name,
        recipientEmail,
        recipientName,
        senderType: currentUser.type || 'admin',
        recipientType,
        threadId,
        parentId: messageId,
        status: 'sent'
      }
    })

    // Update original message threadId if it didn't have one
    if (!originalMessage.threadId) {
      await prisma.message.update({
        where: { id: originalMessage.id },
        data: { threadId }
      })
    }

    // Send email notification
    try {
      const emailHtml = generateMessageEmail({
        subject: replyMessage.subject,
        message: content,
        senderName: currentUser.name,
        senderEmail: currentUser.email,
        recipientName,
        isReply: true,
        originalSubject: originalMessage.subject
      })

      await sendEmail({
        to: recipientEmail,
        subject: `Reply from ${currentUser.name}: ${replyMessage.subject}`,
        html: emailHtml
      })

      // Update message status to delivered
      await prisma.message.update({
        where: { id: replyMessage.id },
        data: {
          status: 'delivered',
          deliveredAt: new Date()
        }
      })
    } catch (emailError) {
      console.error('Error sending reply email:', emailError)
      // Update message with error but don't fail the request
      await prisma.message.update({
        where: { id: replyMessage.id },
        data: {
          status: 'failed',
          error: 'Failed to send email notification'
        }
      })
    }

    return NextResponse.json({
      success: true,
      messageId: replyMessage.id,
      threadId,
      message: 'Reply sent successfully'
    })

  } catch (error) {
    console.error('Error sending reply:', error)
    return NextResponse.json(
      { error: 'Failed to send reply' },
      { status: 500 }
    )
  }
}
