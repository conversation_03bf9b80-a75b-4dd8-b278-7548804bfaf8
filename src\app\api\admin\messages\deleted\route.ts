import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current user
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { role: true }
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get deleted messages (both sent and received)
    const deletedMessages = await prisma.message.findMany({
      where: {
        OR: [
          { senderId: currentUser.id },
          { recipientId: currentUser.id }
        ],
        isDeleted: true
      },
      include: {
        sender: {
          include: { role: true }
        },
        recipient: {
          include: { role: true }
        }
      },
      orderBy: {
        sentAt: 'desc'
      }
    })

    // Format messages for response
    const formattedMessages = deletedMessages.map(message => ({
      id: message.id,
      subject: message.subject,
      content: message.content,
      sentAt: message.sentAt,
      readAt: message.readAt,
      deletedAt: message.updatedAt, // Using updatedAt as deletion timestamp
      senderName: message.sender.name,
      senderEmail: message.sender.email,
      senderType: message.sender.role.name,
      recipientName: message.recipient.name,
      recipientEmail: message.recipient.email,
      recipientType: message.recipient.role.name,
      isSent: message.senderId === currentUser.id
    }))

    return NextResponse.json(formattedMessages)
  } catch (error) {
    console.error('Error fetching deleted messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch deleted messages' },
      { status: 500 }
    )
  }
}

// Restore deleted message
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { messageId } = await request.json()

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 })
    }

    // Get current user
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Restore the message
    const restoredMessage = await prisma.message.update({
      where: {
        id: messageId,
        OR: [
          { senderId: currentUser.id },
          { recipientId: currentUser.id }
        ]
      },
      data: {
        isDeleted: false
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Message restored successfully',
      messageId: restoredMessage.id
    })
  } catch (error) {
    console.error('Error restoring message:', error)
    return NextResponse.json(
      { error: 'Failed to restore message' },
      { status: 500 }
    )
  }
}
