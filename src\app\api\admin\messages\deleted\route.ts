import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Get deleted messages (both sent and received)
    const deletedMessages = await prisma.message.findMany({
      where: {
        OR: [
          { senderEmail: currentUser.email },
          { recipientEmail: currentUser.email }
        ],
        isDeleted: true
      },
      orderBy: {
        sentAt: 'desc'
      }
    })

    // Format messages for response
    const formattedMessages = deletedMessages.map(message => ({
      id: message.id,
      subject: message.subject,
      content: message.content,
      sentAt: message.sentAt,
      readAt: message.readAt,
      deletedAt: message.updatedAt, // Using updatedAt as deletion timestamp
      senderName: message.senderName,
      senderEmail: message.senderEmail,
      senderType: message.senderType,
      recipientName: message.recipientName,
      recipientEmail: message.recipientEmail,
      recipientType: message.recipientType,
      isSent: message.senderEmail === currentUser.email
    }))

    return NextResponse.json(formattedMessages)
  } catch (error) {
    console.error('Error fetching deleted messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch deleted messages' },
      { status: 500 }
    )
  }
}

// Restore deleted message
export async function PATCH(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!
    const { messageId } = await request.json()

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 })
    }

    // Restore the message
    const restoredMessage = await prisma.message.update({
      where: {
        id: messageId,
        OR: [
          { senderEmail: currentUser.email },
          { recipientEmail: currentUser.email }
        ]
      },
      data: {
        isDeleted: false
      }
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Message restored successfully',
      messageId: restoredMessage.id
    })
  } catch (error) {
    console.error('Error restoring message:', error)
    return NextResponse.json(
      { error: 'Failed to restore message' },
      { status: 500 }
    )
  }
}
