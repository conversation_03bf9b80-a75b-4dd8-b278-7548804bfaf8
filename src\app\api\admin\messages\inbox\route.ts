import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Get query parameters for pagination
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Get total count for pagination
    const totalCount = await prisma.message.count({
      where: {
        recipientEmail: currentUser.email,
        isDeleted: false
      }
    })

    // Get messages with pagination and selective fields
    const messages = await prisma.message.findMany({
      where: {
        recipientEmail: currentUser.email,
        isDeleted: false
      },
      select: {
        id: true,
        subject: true,
        content: true,
        senderEmail: true,
        senderName: true,
        senderType: true,
        recipientName: true,
        recipientEmail: true,
        recipientType: true,
        sentAt: true,
        readAt: true,
        createdAt: true,
        threadId: true,
        parentId: true,
        status: true,
        replies: {
          where: {
            isDeleted: false
          },
          select: {
            id: true,
            subject: true,
            senderName: true,
            sentAt: true
          },
          orderBy: {
            sentAt: 'asc'
          }
        },
        parent: {
          select: {
            id: true,
            subject: true,
            senderName: true
          }
        }
      },
      orderBy: {
        sentAt: 'desc'
      },
      skip,
      take: limit
    })

    // Get message statistics (optimized with separate queries)
    const [unreadCount, thisWeekCount] = await Promise.all([
      prisma.message.count({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false,
          readAt: null
        }
      }),
      prisma.message.count({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false,
          sentAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
          }
        }
      })
    ])

    const stats = {
      total: totalCount,
      unread: unreadCount,
      read: totalCount - unreadCount,
      thisWeek: thisWeekCount
    }

    const pagination = {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit),
      hasNext: page < Math.ceil(totalCount / limit),
      hasPrev: page > 1
    }

    return NextResponse.json({
      success: true,
      messages,
      stats,
      pagination,
      message: 'Inbox messages retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching inbox messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch inbox messages' },
      { status: 500 }
    )
  }
}
