import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Get query parameters for pagination
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100)
    const skip = (page - 1) * limit

    // Use parallel queries for better performance
    const [totalCount, messages, unreadCount, thisWeekCount] = await Promise.all([
      prisma.message.count({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false
        }
      }),
      prisma.message.findMany({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false
        },
        select: {
          id: true,
          subject: true,
          content: true,
          senderEmail: true,
          senderName: true,
          senderType: true,
          recipientName: true,
          recipientEmail: true,
          recipientType: true,
          sentAt: true,
          readAt: true,
          createdAt: true,
          threadId: true,
          parentId: true,
          status: true
        },
        orderBy: {
          sentAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.message.count({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false,
          readAt: null
        }
      }),
      prisma.message.count({
        where: {
          recipientEmail: currentUser.email,
          isDeleted: false,
          sentAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    // Get thread information for messages that have threads
    const threadIds = [...new Set(messages.map(m => m.threadId).filter(Boolean))]
    const threadMessages = threadIds.length > 0 ? await prisma.message.findMany({
      where: {
        threadId: { in: threadIds },
        isDeleted: false
      },
      select: {
        id: true,
        subject: true,
        senderName: true,
        sentAt: true,
        threadId: true,
        parentId: true,
        content: true
      },
      orderBy: {
        sentAt: 'asc'
      }
    }) : []

    // Organize thread messages by threadId
    const messagesByThread = threadMessages.reduce((acc, msg) => {
      if (!acc[msg.threadId!]) acc[msg.threadId!] = []
      acc[msg.threadId!].push(msg)
      return acc
    }, {} as Record<string, any[]>)

    // Attach thread information to messages
    const messagesWithThreads = messages.map(message => ({
      ...message,
      threadMessages: message.threadId ? messagesByThread[message.threadId] || [] : [],
      replyCount: message.threadId ? (messagesByThread[message.threadId] || []).length - 1 : 0
    }))

    // Get message statistics
    const stats = {
      total: totalCount,
      unread: unreadCount,
      read: totalCount - unreadCount,
      thisWeek: thisWeekCount
    }

    const pagination = {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit),
      hasNext: page < Math.ceil(totalCount / limit),
      hasPrev: page > 1
    }

    return NextResponse.json({
      success: true,
      messages: messagesWithThreads,
      stats,
      pagination,
      message: 'Inbox messages retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching inbox messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch inbox messages' },
      { status: 500 }
    )
  }
}
