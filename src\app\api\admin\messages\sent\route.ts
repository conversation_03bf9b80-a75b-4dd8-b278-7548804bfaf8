import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current user
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { role: true }
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get sent messages
    const sentMessages = await prisma.message.findMany({
      where: {
        senderId: currentUser.id,
        isDeleted: false
      },
      include: {
        sender: {
          include: { role: true }
        },
        recipient: {
          include: { role: true }
        }
      },
      orderBy: {
        sentAt: 'desc'
      }
    })

    // Format messages for response
    const formattedMessages = sentMessages.map(message => ({
      id: message.id,
      subject: message.subject,
      content: message.content,
      sentAt: message.sentAt,
      readAt: message.readAt,
      senderName: message.sender.name,
      senderEmail: message.sender.email,
      senderType: message.sender.role.name,
      recipientName: message.recipient.name,
      recipientEmail: message.recipient.email,
      recipientType: message.recipient.role.name
    }))

    return NextResponse.json(formattedMessages)
  } catch (error) {
    console.error('Error fetching sent messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sent messages' },
      { status: 500 }
    )
  }
}
