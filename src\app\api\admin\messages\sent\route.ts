import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Get sent messages (excluding deleted)
    const sentMessages = await prisma.message.findMany({
      where: {
        senderEmail: currentUser.email,
        isDeleted: false
      },
      include: {
        replies: {
          where: {
            isDeleted: false
          },
          orderBy: {
            sentAt: 'asc'
          }
        },
        parent: true
      },
      orderBy: {
        sentAt: 'desc'
      }
    })

    // Format messages for response
    const formattedMessages = sentMessages.map(message => ({
      id: message.id,
      subject: message.subject,
      content: message.content,
      sentAt: message.sentAt,
      readAt: message.readAt,
      senderName: message.senderName,
      senderEmail: message.senderEmail,
      senderType: message.senderType,
      recipientName: message.recipientName,
      recipientEmail: message.recipientEmail,
      recipientType: message.recipientType,
      threadId: message.threadId,
      parentId: message.parentId,
      replies: message.replies,
      parent: message.parent,
      createdAt: message.createdAt
    }))

    return NextResponse.json(formattedMessages)
  } catch (error) {
    console.error('Error fetching sent messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sent messages' },
      { status: 500 }
    )
  }
}
