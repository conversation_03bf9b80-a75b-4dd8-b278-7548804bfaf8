import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { sendEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Only allow Super Admin and Admin to test email
    if (!['Super Admin', 'Admin'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const { testEmail } = body

    if (!testEmail) {
      return NextResponse.json(
        { error: 'Test email address is required' },
        { status: 400 }
      )
    }

    console.log('Testing email configuration...')
    console.log('Test email will be sent to:', testEmail)

    // Check environment variables
    const emailConfig = {
      smtpHost: process.env.SMTP_HOST,
      smtpPort: process.env.SMTP_PORT,
      smtpUser: process.env.SMTP_USER,
      smtpPass: process.env.SMTP_PASS ? '***configured***' : 'not configured',
      fromName: process.env.EMAIL_FROM_NAME,
      replyTo: process.env.EMAIL_REPLY_TO
    }

    console.log('Email configuration:', emailConfig)

    // Test email HTML
    const testEmailHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 10px 10px; }
        .success { color: #28a745; font-weight: bold; }
        .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="margin: 0;">✅ Email Test Successful</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Youth Registration System</p>
        </div>

        <div class="content">
            <h2 style="color: #667eea; margin-top: 0;">Email Configuration Test</h2>
            
            <p class="success">🎉 Congratulations! Your email system is working correctly.</p>
            
            <div class="info">
                <h3 style="margin: 0 0 10px 0;">Test Details:</h3>
                <p style="margin: 0;"><strong>Sent by:</strong> ${currentUser.name} (${currentUser.email})</p>
                <p style="margin: 5px 0 0 0;"><strong>Test time:</strong> ${new Date().toLocaleString()}</p>
                <p style="margin: 5px 0 0 0;"><strong>System:</strong> Youth Registration System</p>
            </div>

            <p>This test email confirms that:</p>
            <ul>
                <li>✅ SMTP configuration is correct</li>
                <li>✅ Email authentication is working</li>
                <li>✅ Email delivery is functional</li>
                <li>✅ Message system is ready to use</li>
            </ul>

            <p>You can now send messages through the system with confidence!</p>
        </div>
    </div>
</body>
</html>`

    try {
      console.log('Attempting to send test email...')
      
      const result = await sendEmail({
        to: testEmail,
        subject: '✅ Email Test - Youth Registration System',
        html: testEmailHtml
      })

      console.log('Test email sent successfully:', result)

      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        emailConfig: {
          ...emailConfig,
          smtpPass: undefined // Don't return password
        },
        result
      })

    } catch (emailError) {
      console.error('Test email failed:', emailError)
      
      return NextResponse.json({
        success: false,
        error: 'Test email failed',
        details: emailError instanceof Error ? emailError.message : 'Unknown email error',
        emailConfig: {
          ...emailConfig,
          smtpPass: undefined // Don't return password
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Email test error:', error)
    return NextResponse.json(
      { 
        error: 'Email test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
