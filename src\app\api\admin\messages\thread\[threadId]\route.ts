import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { threadId: string } }
) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!
    const threadId = params.threadId

    // Get all messages in the thread where user is sender or recipient
    const threadMessages = await prisma.message.findMany({
      where: {
        threadId,
        OR: [
          { senderEmail: currentUser.email },
          { recipientEmail: currentUser.email }
        ]
      },
      include: {
        replies: {
          where: {
            OR: [
              { senderEmail: currentUser.email },
              { recipientEmail: currentUser.email }
            ]
          },
          orderBy: {
            sentAt: 'asc'
          }
        },
        parent: true
      },
      orderBy: {
        sentAt: 'asc'
      }
    })

    if (threadMessages.length === 0) {
      return NextResponse.json(
        { error: 'Thread not found or access denied' },
        { status: 404 }
      )
    }

    // Build thread hierarchy
    const threadHierarchy = buildThreadHierarchy(threadMessages)

    return NextResponse.json({
      success: true,
      thread: threadHierarchy,
      threadId,
      messageCount: threadMessages.length,
      message: 'Thread retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching thread:', error)
    return NextResponse.json(
      { error: 'Failed to fetch thread' },
      { status: 500 }
    )
  }
}

function buildThreadHierarchy(messages: any[]) {
  const messageMap = new Map()
  const rootMessages = []

  // First pass: create map of all messages
  messages.forEach(msg => {
    messageMap.set(msg.id, { ...msg, children: [] })
  })

  // Second pass: build hierarchy
  messages.forEach(msg => {
    if (msg.parentId && messageMap.has(msg.parentId)) {
      messageMap.get(msg.parentId).children.push(messageMap.get(msg.id))
    } else {
      rootMessages.push(messageMap.get(msg.id))
    }
  })

  return rootMessages
}
