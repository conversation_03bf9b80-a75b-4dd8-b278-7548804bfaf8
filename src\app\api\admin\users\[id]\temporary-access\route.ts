import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/auth-helpers'
import { prisma } from '@/lib/db'

interface RouteParams {
  params: {
    id: string
  }
}

// Grant temporary access elevation
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!
    const { id: targetUserId } = params

    // Only Super Admin and Admin can grant temporary access
    if (!['Super Admin', 'Admin'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const { 
      temporaryRole, 
      duration, // in hours
      reason,
      permissions = [] 
    } = body

    // Validate required fields
    if (!temporaryRole || !duration || !reason) {
      return NextResponse.json(
        { error: 'Temporary role, duration, and reason are required' },
        { status: 400 }
      )
    }

    // Validate duration (max 72 hours)
    if (duration > 72) {
      return NextResponse.json(
        { error: 'Maximum temporary access duration is 72 hours' },
        { status: 400 }
      )
    }

    // Get target user
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId },
      include: { role: true }
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Calculate expiration time
    const expiresAt = new Date(Date.now() + duration * 60 * 60 * 1000)

    // Check if user already has active temporary access
    const existingAccess = await prisma.temporaryAccess.findFirst({
      where: {
        userId: targetUserId,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    if (existingAccess) {
      return NextResponse.json(
        { error: 'User already has active temporary access' },
        { status: 400 }
      )
    }

    // Create temporary access record
    const temporaryAccess = await prisma.temporaryAccess.create({
      data: {
        userId: targetUserId,
        originalRoleId: targetUser.roleId,
        temporaryRole,
        permissions: permissions.length > 0 ? JSON.stringify(permissions) : null,
        grantedBy: currentUser.email,
        grantedByName: currentUser.name,
        reason,
        expiresAt,
        isActive: true
      }
    })

    // Get the temporary role
    const tempRole = await prisma.role.findFirst({
      where: { name: temporaryRole },
      include: { permissions: true }
    })

    if (tempRole) {
      // Update user's role temporarily
      await prisma.user.update({
        where: { id: targetUserId },
        data: { roleId: tempRole.id }
      })
    }

    // Create notification for the user
    await prisma.notification.create({
      data: {
        type: 'access_granted',
        title: 'Temporary Access Granted',
        message: `You have been granted temporary ${temporaryRole} access for ${duration} hours. Reason: ${reason}`,
        priority: 'high',
        recipientId: targetUserId,
        metadata: JSON.stringify({
          temporaryAccessId: temporaryAccess.id,
          temporaryRole,
          duration,
          expiresAt: expiresAt.toISOString()
        })
      }
    })

    // Create audit log
    await prisma.notification.create({
      data: {
        type: 'admin_action',
        title: 'Temporary Access Granted',
        message: `${currentUser.name} granted temporary ${temporaryRole} access to ${targetUser.name} for ${duration} hours`,
        priority: 'medium',
        recipientId: null, // Global notification for admins
        authorizedBy: currentUser.name,
        authorizedByEmail: currentUser.email,
        metadata: JSON.stringify({
          action: 'grant_temporary_access',
          targetUserId,
          targetUserName: targetUser.name,
          temporaryRole,
          duration,
          reason
        })
      }
    })

    return NextResponse.json({
      message: 'Temporary access granted successfully',
      temporaryAccess: {
        id: temporaryAccess.id,
        temporaryRole,
        duration,
        expiresAt,
        reason
      }
    })

  } catch (error) {
    console.error('Error granting temporary access:', error)
    return NextResponse.json(
      { error: 'Failed to grant temporary access' },
      { status: 500 }
    )
  }
}

// Revoke temporary access
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!
    const { id: targetUserId } = params

    // Only Super Admin and Admin can revoke temporary access
    if (!['Super Admin', 'Admin'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Find active temporary access
    const temporaryAccess = await prisma.temporaryAccess.findFirst({
      where: {
        userId: targetUserId,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      },
      include: {
        user: {
          include: { role: true }
        }
      }
    })

    if (!temporaryAccess) {
      return NextResponse.json(
        { error: 'No active temporary access found for this user' },
        { status: 404 }
      )
    }

    // Restore original role
    await prisma.user.update({
      where: { id: targetUserId },
      data: { roleId: temporaryAccess.originalRoleId }
    })

    // Deactivate temporary access
    await prisma.temporaryAccess.update({
      where: { id: temporaryAccess.id },
      data: { 
        isActive: false,
        revokedBy: currentUser.email,
        revokedByName: currentUser.name,
        revokedAt: new Date()
      }
    })

    // Create notification for the user
    await prisma.notification.create({
      data: {
        type: 'access_revoked',
        title: 'Temporary Access Revoked',
        message: `Your temporary ${temporaryAccess.temporaryRole} access has been revoked by ${currentUser.name}`,
        priority: 'high',
        recipientId: targetUserId,
        metadata: JSON.stringify({
          temporaryAccessId: temporaryAccess.id,
          revokedBy: currentUser.name
        })
      }
    })

    // Create audit log
    await prisma.notification.create({
      data: {
        type: 'admin_action',
        title: 'Temporary Access Revoked',
        message: `${currentUser.name} revoked temporary access from ${temporaryAccess.user.name}`,
        priority: 'medium',
        recipientId: null,
        authorizedBy: currentUser.name,
        authorizedByEmail: currentUser.email,
        metadata: JSON.stringify({
          action: 'revoke_temporary_access',
          targetUserId,
          targetUserName: temporaryAccess.user.name,
          temporaryRole: temporaryAccess.temporaryRole
        })
      }
    })

    return NextResponse.json({
      message: 'Temporary access revoked successfully'
    })

  } catch (error) {
    console.error('Error revoking temporary access:', error)
    return NextResponse.json(
      { error: 'Failed to revoke temporary access' },
      { status: 500 }
    )
  }
}

// Get temporary access status
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const { id: targetUserId } = params

    // Get active temporary access
    const temporaryAccess = await prisma.temporaryAccess.findFirst({
      where: {
        userId: targetUserId,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    return NextResponse.json({
      hasTemporaryAccess: !!temporaryAccess,
      temporaryAccess: temporaryAccess ? {
        id: temporaryAccess.id,
        temporaryRole: temporaryAccess.temporaryRole,
        grantedBy: temporaryAccess.grantedByName,
        reason: temporaryAccess.reason,
        expiresAt: temporaryAccess.expiresAt,
        timeRemaining: Math.max(0, Math.floor((temporaryAccess.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60))) // hours
      } : null
    })

  } catch (error) {
    console.error('Error getting temporary access status:', error)
    return NextResponse.json(
      { error: 'Failed to get temporary access status' },
      { status: 500 }
    )
  }
}
