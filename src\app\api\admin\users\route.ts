import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { hashPassword } from '@/lib/auth'
import { NotificationService } from '@/lib/notifications'
import { authenticateRequest, hasPermission } from '@/lib/auth-helpers'
import { getDefaultUserRole, getMaxUsers, getPasswordRequirements } from '@/lib/settings'
import { validatePassword } from '@/lib/password-validation'
import { filterManageableUsers, getAssignableRoles } from '@/lib/role-hierarchy'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to read users (<PERSON> <PERSON>min, <PERSON><PERSON>, Manager)
    if (!hasPermission(currentUser, 'users:read') && !['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Fetch all users with their roles
    const allUsersFromDB = await prisma.user.findMany({
      include: {
        role: {
          include: {
            permissions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Filter users based on role hierarchy
    const users = filterManageableUsers(allUsersFromDB, currentUser.role?.name || '')

    // Also include admins in the user list
    const allAdmins = await prisma.admin.findMany({
      include: {
        role: {
          include: {
            permissions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Filter admins based on role hierarchy
    const admins = filterManageableUsers(allAdmins, currentUser.role?.name || '')

    // Combine and format the data
    const combinedUsers = [
      ...admins.map(admin => ({
        id: admin.id,
        email: admin.email,
        name: admin.name,
        isActive: admin.isActive,
        lastLogin: admin.lastLogin,
        createdAt: admin.createdAt,
        type: 'admin',
        role: admin.role || {
          id: 'no-role',
          name: 'No Role',
          description: 'No role assigned',
          isSystem: false
        }
      })),
      ...users.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        type: 'user',
        role: user.role
      }))
    ]

    return NextResponse.json({
      users: combinedUsers,
      total: combinedUsers.length
    })

  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to create users (Super Admin, Admin, Manager)
    if (!hasPermission(currentUser, 'users:write') && !['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Check maximum users limit
    const maxUsers = await getMaxUsers()
    const currentUserCount = await prisma.user.count()

    if (currentUserCount >= maxUsers) {
      return NextResponse.json({
        error: `Maximum number of users (${maxUsers}) has been reached. Please contact your administrator to increase the limit.`
      }, { status: 400 })
    }

    const body = await request.json()
    let { email, name, password, roleId, type = 'user' } = body

    // Validate required fields
    if (!email || !name || !password) {
      return NextResponse.json(
        { error: 'Email, name, and password are required' },
        { status: 400 }
      )
    }

    // Validate password requirements
    const passwordRequirement = await getPasswordRequirements()
    const passwordValidation = validatePassword(password, passwordRequirement)

    if (!passwordValidation.isValid) {
      return NextResponse.json({
        error: `Password does not meet requirements: ${passwordValidation.errors.join(', ')}`
      }, { status: 400 })
    }

    // If no roleId provided, use default role from settings
    if (!roleId) {
      const defaultRoleName = await getDefaultUserRole()
      const defaultRole = await prisma.role.findUnique({
        where: { name: defaultRoleName }
      })

      if (defaultRole) {
        roleId = defaultRole.id
      } else {
        // Fallback to Viewer role if default role not found
        const viewerRole = await prisma.role.findUnique({
          where: { name: 'Viewer' }
        })
        roleId = viewerRole?.id
      }

      if (!roleId) {
        return NextResponse.json(
          { error: 'No valid role found. Please specify a role.' },
          { status: 400 }
        )
      }
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    const existingAdmin = await prisma.admin.findUnique({
      where: { email }
    })

    if (existingUser || existingAdmin) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      )
    }

    // Verify role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      )
    }

    // Check if current user can assign this role based on role hierarchy
    const assignableRoles = getAssignableRoles(currentUser.role?.name || '')
    if (!assignableRoles.includes(role.name)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign this role' },
        { status: 403 }
      )
    }

    // Hash password
    const bcrypt = require('bcryptjs')
    const hashedPassword = await bcrypt.hash(password, 10)

    // Create user
    const newUser = await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
        roleId,
        createdBy: currentUser.id
      },
      include: {
        role: true
      }
    })

    // Create notification for user creation
    try {
      await NotificationService.create({
        type: 'user_created',
        title: 'New User Account Created',
        message: `A new user account has been created for ${newUser.name} (${newUser.email}).`,
        priority: 'medium',
        authorizedBy: currentUser.name || currentUser.email,
        authorizedByEmail: currentUser.email,
        metadata: {
          userId: newUser.id,
          userEmail: newUser.email,
          userName: newUser.name,
          createdBy: currentUser.id,
          createdByName: currentUser.name,
          createdByEmail: currentUser.email
        }
      })
    } catch (notificationError) {
      console.error('Failed to create notification:', notificationError)
      // Don't fail the user creation if notification fails
    }

    return NextResponse.json({
      message: 'User created successfully',
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        isActive: newUser.isActive,
        role: newUser.role
      }
    })

  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}
