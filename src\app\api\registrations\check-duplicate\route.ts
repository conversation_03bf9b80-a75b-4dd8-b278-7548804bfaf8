import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { emailAddress, phoneNumber } = await request.json()

    if (!emailAddress || !phoneNumber) {
      return NextResponse.json(
        { error: 'Email address and phone number are required' },
        { status: 400 }
      )
    }

    // Check for existing registration with same email or phone
    const existingRegistration = await prisma.registration.findFirst({
      where: {
        OR: [
          { emailAddress: emailAddress.toLowerCase().trim() },
          { phoneNumber: phoneNumber.trim() }
        ]
      },
      select: {
        id: true,
        fullName: true,
        emailAddress: true,
        phoneNumber: true,
        createdAt: true
      }
    })

    // Also check for similar names to catch potential duplicates
    const similarNameRegistration = await prisma.registration.findFirst({
      where: {
        AND: [
          { emailAddress: emailAddress.toLowerCase().trim() },
          { phoneNumber: phoneNumber.trim() }
        ]
      },
      select: {
        id: true,
        fullName: true,
        emailAddress: true,
        phoneNumber: true,
        createdAt: true
      }
    })

    if (existingRegistration) {
      return NextResponse.json({
        isDuplicate: true,
        existingRegistration: {
          id: existingRegistration.id,
          fullName: existingRegistration.fullName,
          emailAddress: existingRegistration.emailAddress,
          phoneNumber: existingRegistration.phoneNumber,
          registrationDate: existingRegistration.createdAt
        }
      })
    }

    return NextResponse.json({
      isDuplicate: false
    })

  } catch (error) {
    console.error('Duplicate check error:', error)
    return NextResponse.json(
      { error: 'Failed to check for duplicate registration' },
      { status: 500 }
    )
  }
}
