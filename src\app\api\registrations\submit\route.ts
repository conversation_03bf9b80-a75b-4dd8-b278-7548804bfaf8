import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { sendRegistrationNotification } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    const requiredFields = [
      'fullName',
      'dateOfBirth',
      'gender',
      'address',
      'phoneNumber',
      'emailAddress',
      'parentGuardianName',
      'parentGuardianPhone',
      'parentGuardianEmail'
    ]

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Convert dateOfBirth to Date object
    const dateOfBirth = new Date(data.dateOfBirth)
    if (isNaN(dateOfBirth.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date of birth' },
        { status: 400 }
      )
    }

    // Create registration
    const registration = await prisma.registration.create({
      data: {
        fullName: data.fullName,
        dateOfBirth: dateOfBirth,
        gender: data.gender,
        address: data.address,
        phoneNumber: data.phoneNumber,
        emailAddress: data.emailAddress,
        // Use parent/guardian as emergency contact since emergency contact is removed
        emergencyContactName: data.parentGuardianName,
        emergencyContactRelationship: 'Parent/Guardian',
        emergencyContactPhone: data.parentGuardianPhone,
        parentGuardianName: data.parentGuardianName,
        parentGuardianPhone: data.parentGuardianPhone,
        parentGuardianEmail: data.parentGuardianEmail,
        roommateRequestConfirmationNumber: null, // Field removed from form
        medications: data.medications || null,
        allergies: data.allergies || null,
        specialNeeds: data.specialNeeds || null,
        dietaryRestrictions: data.dietaryRestrictions || null,
        parentalPermissionGranted: true, // Always mark as completed/approved
        parentalPermissionDate: new Date() // Set current date as completion date
      }
    })

    // Create notification record in database
    try {
      await prisma.notification.create({
        data: {
          type: 'new_registration',
          title: 'New Registration',
          message: `${registration.fullName} has registered for the youth program`,
          priority: 'medium',
          metadata: JSON.stringify({
            registrationId: registration.id,
            participantName: registration.fullName,
            participantEmail: registration.emailAddress,
            participantPhone: registration.phoneNumber,
            parentGuardian: registration.parentGuardianName,
            registrationDate: registration.createdAt
          })
        }
      })
      console.log('Database notification created for:', registration.fullName)
    } catch (notificationError) {
      console.error('Failed to create notification record:', notificationError)
      // Don't fail the registration if notification creation fails
    }

    // Send notification email to admins (don't wait for it to complete)
    try {
      await sendRegistrationNotification(registration)
      console.log('Registration notification sent for:', registration.fullName)
    } catch (emailError) {
      console.error('Failed to send registration notification:', emailError)
      // Don't fail the registration if email fails
    }

    return NextResponse.json({
      success: true,
      registrationId: registration.id,
      message: 'Registration submitted successfully'
    })
  } catch (error) {
    console.error('Registration submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
