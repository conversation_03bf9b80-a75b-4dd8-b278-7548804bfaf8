import { NextRequest, NextResponse } from 'next/server'
import { sendEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, type } = body

    let subject = ''
    let htmlContent = ''

    switch (type) {
      case 'registration':
        subject = '🎉 Test Registration Notification'
        htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Registration Notification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Test Registration Notification</h1>
            <p>This is a test email to verify the notification system is working</p>
        </div>
        
        <div class="content">
            <h2>Test Registration Details</h2>
            <p><strong>Participant:</strong> Test User</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> (*************</p>
            <p><strong>Age:</strong> 16 years old</p>
            <p><strong>Registration Date:</strong> ${new Date().toLocaleDateString()}</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="http://localhost:3001/admin/registrations" class="button">
                    View Registration Details
                </a>
            </div>
            
            <p><strong>Status:</strong> ✅ Email notification system is working correctly!</p>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This is a test notification from the Youth Registration System</p>
        </div>
    </div>
</body>
</html>`
        break

      case 'system':
        subject = '🔔 System Notification Test'
        htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>System Notification Test</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 8px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 System Notification Test</h1>
            <p>Testing system-wide notifications</p>
        </div>
        
        <div class="content">
            <h2>System Status</h2>
            <p>✅ Email service is operational</p>
            <p>✅ Notification system is working</p>
            <p>✅ SMTP configuration is correct</p>
            <p><strong>Test Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
    </div>
</body>
</html>`
        break

      default:
        subject = '📧 Test Email Notification'
        htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Email</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #667eea; color: white; padding: 20px; border-radius: 8px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Test Email</h1>
            <p>This is a test email from the Youth Registration System</p>
            <p>Sent at: ${new Date().toLocaleString()}</p>
        </div>
    </div>
</body>
</html>`
    }

    // Send the test email
    const result = await sendEmail({
      to: email,
      subject: subject,
      html: htmlContent
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Test notification sent successfully',
        messageId: result.messageId
      })
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to send test notification'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Test notification error:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to send test notification',
      error: error.message
    }, { status: 500 })
  }
}
