import { NextResponse } from 'next/server'
import { getSessionTimeout } from '@/lib/settings'

export async function GET() {
  try {
    const sessionTimeout = await getSessionTimeout()
    
    return NextResponse.json({
      success: true,
      sessionTimeoutHours: sessionTimeout,
      message: `Session timeout is set to ${sessionTimeout} hours`
    })
  } catch (error) {
    console.error('Error getting session timeout:', error)
    return NextResponse.json(
      { error: 'Failed to get session timeout' },
      { status: 500 }
    )
  }
}
