import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "MopgomYouth - Registration System",
  description: "Youth program registration and management system",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="light">
      <body className="font-apercu antialiased text-gray-900 bg-white">
        {children}
      </body>
    </html>
  );
}
