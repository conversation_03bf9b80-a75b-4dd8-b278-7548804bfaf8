'use client'

import { useState } from 'react'
import Link from 'next/link'
import { FormData, validateStep1, validateStep2, validateAllSteps, ValidationError, checkDuplicateRegistration } from '@/lib/validation'
import { ModernDatePicker } from '@/components/ui/modern-date-picker'

export default function RegisterPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    phoneNumber: '',
    emailAddress: '',
    emergencyContactName: '',
    emergencyContactRelationship: '',
    emergencyContactPhone: '',
    parentGuardianName: '',
    parentGuardianPhone: '',
    parentGuardianEmail: '',
    medications: '',
    allergies: '',
    specialNeeds: '',
    dietaryRestrictions: '',

  })

  const [errors, setErrors] = useState<ValidationError[]>([])
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [submitError, setSubmitError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [duplicateWarning, setDuplicateWarning] = useState<{
    show: boolean
    existingRegistration?: any
  }>({ show: false })

  const totalSteps = 2

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))

    // Clear errors for this field
    setErrors(prev => prev.filter(error => error.field !== name))

    // Clear duplicate warning and submit error when user changes email or phone
    if (name === 'emailAddress' || name === 'phoneNumber') {
      setDuplicateWarning({ show: false })
      setSubmitError('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Only handle Enter key on input, select, and textarea elements
    const target = e.target as HTMLElement
    const isFormField = target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA'

    // Prevent Enter key from submitting form unless on final step and from a form field
    if (e.key === 'Enter' && isFormField && currentStep !== totalSteps) {
      e.preventDefault()
      e.stopPropagation()
      console.log('Enter pressed on step', currentStep, 'preventing submission and advancing to next step')
      handleNext()
    }
  }

  const getFieldError = (fieldName: string): string | undefined => {
    return errors.find(error => error.field === fieldName)?.message
  }

  const calculateAge = (dateOfBirth: string): number => {
    if (!dateOfBirth) return 0
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }



  const validateCurrentStep = (): boolean => {
    let stepErrors: ValidationError[] = []

    switch (currentStep) {
      case 1:
        stepErrors = validateStep1(formData)
        break
      case 2:
        stepErrors = validateStep2(formData)
        break
    }

    setErrors(stepErrors)
    return stepErrors.length === 0
  }

  const handleNext = () => {
    console.log('handleNext called on step', currentStep)
    if (validateCurrentStep()) {
      const newStep = Math.min(currentStep + 1, totalSteps)
      console.log('Validation passed, advancing from step', currentStep, 'to step', newStep)
      setCurrentStep(newStep)
    } else {
      console.log('Validation failed on step', currentStep)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submit triggered on step', currentStep, 'of', totalSteps, 'isSubmitting:', isSubmitting)

    // Prevent multiple submissions
    if (isSubmitting) {
      console.log('Blocking submission - already submitting')
      return
    }

    // Only allow submission on the final step
    if (currentStep !== totalSteps) {
      console.log('Blocking submission - not on final step')
      return
    }

    console.log('Proceeding with form submission')
    setIsSubmitting(true)

    // Validate all steps before submission
    const allErrors = validateAllSteps(formData)
    if (allErrors.length > 0) {
      setErrors(allErrors)
      setSubmitError('Please fill all Input before submitting')
      setIsSubmitting(false)
      return
    }

    setLoading(true)
    setSubmitError('')
    setDuplicateWarning({ show: false })

    try {
      // Check for duplicate registration first
      console.log('Checking for duplicate registration...')
      const duplicateCheck = await checkDuplicateRegistration(formData.emailAddress, formData.phoneNumber)

      if (duplicateCheck.isDuplicate) {
        setDuplicateWarning({
          show: true,
          existingRegistration: duplicateCheck.existingRegistration
        })

        // Add specific field errors for duplicate email and phone
        const duplicateErrors: ValidationError[] = []

        // Check which field(s) caused the duplicate
        if (duplicateCheck.existingRegistration.emailAddress === formData.emailAddress.toLowerCase().trim()) {
          duplicateErrors.push({
            field: 'emailAddress',
            message: 'This email address is already Registered'
          })
        }

        if (duplicateCheck.existingRegistration.phoneNumber === formData.phoneNumber.trim()) {
          duplicateErrors.push({
            field: 'phoneNumber',
            message: 'This phone number is already Registered'
          })
        }

        setErrors(duplicateErrors)
        setSubmitError(`A Registration already exists with this Email or Phone Number. Registration found for "${duplicateCheck.existingRegistration.fullName}" on ${new Date(duplicateCheck.existingRegistration.registrationDate).toLocaleDateString()}.`)

        // Navigate back to step 1 to show the highlighted fields
        setCurrentStep(1)

        setLoading(false)
        setIsSubmitting(false)
        return
      }

      console.log('No duplicate found, Proceeding with Registration...')
      const response = await fetch('/api/registrations/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
      } else {
        setSubmitError(data.error || 'Registration failed')
      }
    } catch (error) {
      console.error('Registration error:', error)
      setSubmitError('Network error. Please try again.')
    } finally {
      setLoading(false)
      setIsSubmitting(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-20 w-20 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Registration Complete!
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Welcome to our youth program! Your registration has been successfully completed.
            </p>
            <p className="mt-2 text-sm text-gray-500">
              You're all set! We'll be in touch with program details and next steps soon.
            </p>
            <div className="mt-8">
              <Link
                href="/"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-6 sm:py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <Link href="/" className="inline-flex items-center font-apercu-medium text-indigo-600 hover:text-indigo-500 mb-4 sm:mb-6 transition-colors duration-200">
            <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Home
          </Link>
          <div className="mb-4 sm:mb-6">
            <div className="h-12 w-12 sm:h-16 sm:w-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4">
              <svg className="h-6 w-6 sm:h-8 sm:w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h1 className="font-apercu-bold text-2xl sm:text-4xl text-gray-900 mb-2 sm:mb-3">Youth Program Registration</h1>
            <p className="font-apercu-regular text-sm sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
              Join our amazing community of young leaders and changemakers. Take the first step towards an incredible journey of growth and discovery.
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6 sm:mb-10">
          <div className="flex items-center justify-center px-4">
            {[1, 2].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 sm:w-12 sm:h-12 rounded-full border-2 transition-all duration-300 ${
                  step <= currentStep
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 border-transparent text-white shadow-lg'
                    : 'bg-white border-gray-300 text-gray-500 hover:border-gray-400'
                }`}>
                  {step < currentStep ? (
                    <svg className="w-4 h-4 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <span className="font-apercu-bold text-xs sm:text-sm">{step}</span>
                  )}
                </div>
                {step < 2 && (
                  <div className={`w-12 sm:w-20 h-1 sm:h-2 mx-2 sm:mx-3 rounded-full transition-all duration-300 ${
                    step < currentStep ? 'bg-gradient-to-r from-indigo-600 to-purple-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-3 sm:mt-4">
            <div className="text-center">
              <div className="font-apercu-bold text-xs sm:text-sm text-gray-900 mb-1">
                Step {currentStep} of {totalSteps}
              </div>
              <div className="font-apercu-regular text-xs sm:text-sm text-gray-600">
                {currentStep === 1 ? 'Personal Information' :
                 'Guardian & Additional Info'}
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow-2xl rounded-xl sm:rounded-2xl overflow-hidden border border-gray-100">
          <form onSubmit={handleSubmit} onKeyDown={handleKeyDown}>
            <div className="px-4 sm:px-8 py-6 sm:py-10">
              {submitError && (
                <div className="mb-6 sm:mb-8 bg-red-50 border border-red-200 text-red-700 px-4 sm:px-6 py-3 sm:py-4 rounded-xl">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-apercu-medium text-sm sm:text-base">{submitError}</span>
                  </div>
                </div>
              )}

              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center pb-4 sm:pb-6 border-b border-gray-100">
                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4">
                      <svg className="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <h2 className="font-apercu-bold text-xl sm:text-2xl text-gray-900 mb-2">Personal Information</h2>
                    <p className="font-apercu-regular text-sm sm:text-base text-gray-600">Tell us about yourself to get started</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8">
                    <div>
                      <label htmlFor="fullName" className="block font-apercu-medium text-sm text-gray-700 mb-3">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        name="fullName"
                        id="fullName"
                        value={formData.fullName}
                        onChange={handleChange}
                        className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                          getFieldError('fullName') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                        }`}
                        placeholder="Enter your full legal name"
                      />
                      {getFieldError('fullName') && (
                        <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {getFieldError('fullName')}
                        </p>
                      )}
                    </div>

                    <div>
                      <ModernDatePicker
                        label="Date of Birth"
                        value={formData.dateOfBirth}
                        onChange={(date) => setFormData(prev => ({ ...prev, dateOfBirth: date }))}
                        placeholder="Select your date of birth"
                        required
                        error={!!getFieldError('dateOfBirth')}
                        maxDate={new Date().toISOString().split('T')[0]} // Can't be in the future
                        className="font-apercu-regular"
                      />
                      {getFieldError('dateOfBirth') && (
                        <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {getFieldError('dateOfBirth')}
                        </p>
                      )}
                      {formData.dateOfBirth && (
                        <p className="mt-2 text-sm text-gray-600 font-apercu-regular">
                          Age: {calculateAge(formData.dateOfBirth)} years old
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="gender" className="block font-apercu-medium text-sm text-gray-700 mb-3">
                        Gender *
                      </label>
                      <select
                        name="gender"
                        id="gender"
                        value={formData.gender}
                        onChange={handleChange}
                        className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                          getFieldError('gender') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                        }`}
                      >
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                      {getFieldError('gender') && (
                        <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {getFieldError('gender')}
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="phoneNumber" className="block font-apercu-medium text-sm text-gray-700 mb-3">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        name="phoneNumber"
                        id="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={handleChange}
                        className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                          getFieldError('phoneNumber') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                        }`}
                        placeholder="081601234567"
                      />
                      {getFieldError('phoneNumber') && (
                        <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {getFieldError('phoneNumber')}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="emailAddress" className="block font-apercu-medium text-sm text-gray-700 mb-3">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="emailAddress"
                      id="emailAddress"
                      value={formData.emailAddress}
                      onChange={handleChange}
                      className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                        getFieldError('emailAddress') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {getFieldError('emailAddress') && (
                      <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {getFieldError('emailAddress')}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="address" className="block font-apercu-medium text-sm text-gray-700 mb-3">
                      Address *
                    </label>
                    <textarea
                      name="address"
                      id="address"
                      rows={3}
                      value={formData.address}
                      onChange={handleChange}
                      className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                        getFieldError('address') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                      }`}
                      placeholder="Street address, city, state, zip code"
                    />
                    {getFieldError('address') && (
                      <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {getFieldError('address')}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Step 2: Guardian & Additional Information */}
              {currentStep === 2 && (
                <div className="space-y-8">
                  <div className="text-center pb-6 border-b border-gray-100">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <h2 className="font-apercu-bold text-2xl text-gray-900 mb-2">Guardian & Additional Information</h2>
                    <p className="font-apercu-regular text-gray-600">Parent/Guardian details and additional information</p>
                  </div>

                  {/* Parent/Guardian Information - Now Required */}
                  <div className="bg-indigo-50 p-6 rounded-lg border border-indigo-200">
                    <div className="flex items-center mb-4">
                      <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <h3 className="font-apercu-bold text-lg text-gray-900">
                        Parent/Guardian Information *
                      </h3>
                    </div>
                    <p className="font-apercu-regular text-sm text-indigo-700 mb-6">
                      Required for all participants
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="parentGuardianName" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Parent/Guardian Name *
                        </label>
                        <input
                          type="text"
                          name="parentGuardianName"
                          id="parentGuardianName"
                          value={formData.parentGuardianName}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('parentGuardianName') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="Parent or guardian full name"
                        />
                        {getFieldError('parentGuardianName') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('parentGuardianName')}
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="parentGuardianPhone" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Parent/Guardian Phone *
                        </label>
                        <input
                          type="tel"
                          name="parentGuardianPhone"
                          id="parentGuardianPhone"
                          value={formData.parentGuardianPhone}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('parentGuardianPhone') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="081601234567"
                        />
                        {getFieldError('parentGuardianPhone') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('parentGuardianPhone')}
                          </p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label htmlFor="parentGuardianEmail" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Parent/Guardian Email *
                        </label>
                        <input
                          type="email"
                          name="parentGuardianEmail"
                          id="parentGuardianEmail"
                          value={formData.parentGuardianEmail}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('parentGuardianEmail') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {getFieldError('parentGuardianEmail') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('parentGuardianEmail')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <div className="flex items-center mb-4">
                      <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <h3 className="font-apercu-bold text-lg text-gray-900">
                        Additional Information *
                      </h3>
                    </div>
                    <p className="font-apercu-regular text-sm text-gray-600 mb-6">
                      Required information to help us better serve you
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="medications" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Current Medications *
                        </label>
                        <textarea
                          name="medications"
                          id="medications"
                          rows={3}
                          value={formData.medications}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('medications') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="List any current medications (or write 'None')"
                        />
                        {getFieldError('medications') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('medications')}
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="allergies" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Allergies *
                        </label>
                        <textarea
                          name="allergies"
                          id="allergies"
                          rows={3}
                          value={formData.allergies}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('allergies') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="List any allergies (or write 'None')"
                        />
                        {getFieldError('allergies') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('allergies')}
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="specialNeeds" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Special Needs or Accommodations *
                        </label>
                        <textarea
                          name="specialNeeds"
                          id="specialNeeds"
                          rows={3}
                          value={formData.specialNeeds}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('specialNeeds') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="Describe any special needs or accommodations (or write 'None')"
                        />
                        {getFieldError('specialNeeds') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('specialNeeds')}
                          </p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label htmlFor="dietaryRestrictions" className="block font-apercu-medium text-sm text-gray-700 mb-2">
                          Dietary Restrictions *
                        </label>
                        <textarea
                          name="dietaryRestrictions"
                          id="dietaryRestrictions"
                          rows={3}
                          value={formData.dietaryRestrictions}
                          onChange={handleChange}
                          className={`block w-full px-4 py-3 border rounded-xl shadow-sm font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 ${
                            getFieldError('dietaryRestrictions') ? 'border-red-300 focus:border-red-500 bg-red-50' : 'border-gray-300 focus:border-indigo-500 hover:border-gray-400'
                          }`}
                          placeholder="List any dietary restrictions or preferences (or write 'None')"
                        />
                        {getFieldError('dietaryRestrictions') && (
                          <p className="mt-2 text-sm text-red-600 font-apercu-medium flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {getFieldError('dietaryRestrictions')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}


            </div>

            {/* Navigation Buttons */}
            <div className="px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
              <button
                type="button"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className={`inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium w-full sm:w-auto ${
                  currentStep === 1
                    ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                    : 'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                }`}
              >
                <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Previous
              </button>

              <div className="flex space-x-3 w-full sm:w-auto">
                {currentStep < totalSteps ? (
                  <button
                    type="button"
                    onClick={handleNext}
                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto"
                  >
                    Next
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={loading}
                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 w-full sm:w-auto"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="hidden sm:inline">Submitting...</span>
                        <span className="sm:hidden">Submitting...</span>
                      </>
                    ) : (
                      <>
                        <span className="hidden sm:inline">Submit Registration</span>
                        <span className="sm:hidden">Submit</span>
                        <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
