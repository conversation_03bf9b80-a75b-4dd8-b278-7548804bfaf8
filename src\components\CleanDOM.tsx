'use client'

import { useEffect, useState } from 'react'

// Component to clean up browser extension attributes
export function CleanDOM() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // Only run on client to avoid hydration mismatch
    setIsClient(true)

    const cleanupAttributes = () => {
      // List of browser extension attributes to remove
      const extensionAttributes = [
        'bis_register',
        'bis_skin_checked',
        'data-new-gr-c-s-check-loaded',
        'data-gr-ext-installed',
        '__processed_1511e639-5109-4307-8bdb-559ec7333fe1__',
        'data-darkreader-mode',
        'data-darkreader-scheme',
        'data-adblock-key',
        'data-lastpass-icon-root',
        'data-1p-ignore',
        'data-bitwarden-watching'
      ]

      // Remove attributes from all elements
      const removeExtensionAttributes = (element: Element) => {
        extensionAttributes.forEach(attr => {
          if (element.hasAttribute(attr)) {
            element.removeAttribute(attr)
          }
        })
      }

      // Clean current elements
      const allElements = document.querySelectorAll('*')
      allElements.forEach(removeExtensionAttributes)

      // Set up mutation observer to clean new elements
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element
                removeExtensionAttributes(element)
                
                // Clean child elements too
                const childElements = element.querySelectorAll('*')
                childElements.forEach(removeExtensionAttributes)
              }
            })
          } else if (mutation.type === 'attributes') {
            const element = mutation.target as Element
            const attributeName = mutation.attributeName
            
            if (attributeName && extensionAttributes.includes(attributeName)) {
              element.removeAttribute(attributeName)
            }
          }
        })
      })

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: extensionAttributes
      })

      // Cleanup function
      return () => {
        observer.disconnect()
      }
    }

    // Only run cleanup after client hydration
    if (isClient) {
      const timer = setTimeout(cleanupAttributes, 100)
      return () => {
        clearTimeout(timer)
      }
    }
  }, [isClient])

  // Don't render anything during SSR to avoid hydration mismatch
  if (!isClient) {
    return null
  }

  return null // This component doesn't render anything
}

// Hook for manual DOM cleanup
export function useCleanDOM() {
  const cleanupNow = () => {
    const extensionAttributes = [
      'bis_register',
      'bis_skin_checked',
      'data-new-gr-c-s-check-loaded',
      'data-gr-ext-installed',
      '__processed_1511e639-5109-4307-8bdb-559ec7333fe1__',
      'data-darkreader-mode',
      'data-darkreader-scheme',
      'data-adblock-key',
      'data-lastpass-icon-root',
      'data-1p-ignore',
      'data-bitwarden-watching'
    ]

    const allElements = document.querySelectorAll('*')
    allElements.forEach(element => {
      extensionAttributes.forEach(attr => {
        if (element.hasAttribute(attr)) {
          element.removeAttribute(attr)
        }
      })
    })
  }

  return { cleanupNow }
}

// CSS to hide extension elements
export const extensionCleanupCSS = `
  /* Hide common browser extension elements */
  [bis_register],
  [bis_skin_checked],
  [data-new-gr-c-s-check-loaded],
  [data-gr-ext-installed],
  [__processed_1511e639-5109-4307-8bdb-559ec7333fe1__],
  [data-darkreader-mode],
  [data-darkreader-scheme],
  [data-adblock-key],
  [data-lastpass-icon-root],
  [data-1p-ignore],
  [data-bitwarden-watching] {
    /* Don't hide the elements, just clean up the attributes */
  }
  
  /* Hide specific extension injected elements */
  div[id*="lastpass"],
  div[id*="bitwarden"],
  div[id*="grammarly"],
  div[class*="darkreader"],
  iframe[src*="extension"] {
    display: none !important;
  }
`
