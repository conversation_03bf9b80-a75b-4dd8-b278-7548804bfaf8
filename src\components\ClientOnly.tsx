'use client'

import { useEffect, useState } from 'react'

interface ClientOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

// Component that only renders on the client to avoid hydration mismatches
export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// Hook to check if we're on the client
export function useIsClient() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}

// Component for client-side only scripts and effects
export function ClientSideEffects() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Clean up browser extension attributes after hydration
    const cleanupExtensionAttributes = () => {
      const extensionAttributes = [
        'bis_register',
        'bis_skin_checked',
        'data-new-gr-c-s-check-loaded',
        'data-gr-ext-installed',
        '__processed_4c5a8841-7ef7-45bc-9447-f7932321b292__',
        'data-darkreader-mode',
        'data-darkreader-scheme',
        'data-adblock-key',
        'data-lastpass-icon-root',
        'data-1p-ignore',
        'data-bitwarden-watching'
      ]

      // Remove attributes from all elements
      const allElements = document.querySelectorAll('*')
      allElements.forEach(element => {
        extensionAttributes.forEach(attr => {
          if (element.hasAttribute(attr)) {
            element.removeAttribute(attr)
          }
        })
      })

      // Set up mutation observer to clean new elements
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes') {
            const element = mutation.target as Element
            const attributeName = mutation.attributeName
            
            if (attributeName && extensionAttributes.includes(attributeName)) {
              element.removeAttribute(attributeName)
            }
          }
        })
      })

      observer.observe(document.body, {
        attributes: true,
        attributeFilter: extensionAttributes,
        subtree: true
      })

      return () => observer.disconnect()
    }

    // Run cleanup after a delay to ensure DOM is ready
    const timer = setTimeout(cleanupExtensionAttributes, 500)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  if (!isClient) {
    return null
  }

  return null
}

// Safe date formatter that works on both server and client
export function SafeDateFormatter({ 
  date, 
  options = {} 
}: { 
  date: string | Date
  options?: Intl.DateTimeFormatOptions 
}) {
  const [formattedDate, setFormattedDate] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const dateObj = typeof date === 'string' ? new Date(date) : date
    setFormattedDate(dateObj.toLocaleDateString('en-US', options))
  }, [date, options])

  if (!isClient) {
    // Return a safe server-side fallback
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return <span>{dateObj.toISOString().split('T')[0]}</span>
  }

  return <span>{formattedDate}</span>
}

// Safe time formatter
export function SafeTimeFormatter({ 
  date, 
  options = { hour: 'numeric', minute: '2-digit', hour12: true } 
}: { 
  date: string | Date
  options?: Intl.DateTimeFormatOptions 
}) {
  const [formattedTime, setFormattedTime] = useState('')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const dateObj = typeof date === 'string' ? new Date(date) : date
    setFormattedTime(dateObj.toLocaleTimeString('en-US', options))
  }, [date, options])

  if (!isClient) {
    // Return a safe server-side fallback
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const hours = dateObj.getUTCHours()
    const minutes = dateObj.getUTCMinutes()
    return <span>{hours.toString().padStart(2, '0')}:{minutes.toString().padStart(2, '0')}</span>
  }

  return <span>{formattedTime}</span>
}

// Component to suppress hydration warnings for specific elements
export function NoSSR({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient ? <>{children}</> : null
}
