'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Clock, AlertTriangle, RefreshCw } from 'lucide-react'

interface SessionTimeoutProps {
  sessionTimeoutHours?: number
}

export function SessionTimeout({ sessionTimeoutHours = 24 }: SessionTimeoutProps) {
  const [timeLeft, setTimeLeft] = useState<number>(0)
  const [showWarning, setShowWarning] = useState(false)
  const [isActive, setIsActive] = useState(true)
  const router = useRouter()

  // Convert hours to milliseconds
  const sessionTimeoutMs = sessionTimeoutHours * 60 * 60 * 1000
  const warningTimeMs = 5 * 60 * 1000 // Show warning 5 minutes before expiry

  const logout = useCallback(async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      router.push('/admin/login')
    } catch (error) {
      console.error('Logout error:', error)
      router.push('/admin/login')
    }
  }, [router])

  const extendSession = useCallback(async () => {
    try {
      // Make a request to refresh the session
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        // Reset the timer
        const now = Date.now()
        const expiryTime = now + sessionTimeoutMs
        localStorage.setItem('sessionExpiry', expiryTime.toString())
        setShowWarning(false)
        setIsActive(true)
      } else {
        logout()
      }
    } catch (error) {
      console.error('Session extend error:', error)
      logout()
    }
  }, [sessionTimeoutMs, logout])

  const formatTime = (ms: number): string => {
    const totalSeconds = Math.floor(ms / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`
    } else {
      return `${seconds}s`
    }
  }

  useEffect(() => {
    const checkSession = async () => {
      try {
        const response = await fetch('/api/auth/me')
        if (!response.ok) {
          setIsActive(false)
          logout()
          return
        }

        // Get JWT token from cookie and decode to get expiration
        const tokenResponse = await fetch('/api/auth/token-info')
        if (tokenResponse.ok) {
          const tokenInfo = await tokenResponse.json()
          const expiryTime = tokenInfo.exp * 1000 // Convert to milliseconds

          const updateTimer = () => {
            const now = Date.now()
            const remaining = expiryTime - now

            if (remaining <= 0) {
              // Session expired
              setTimeLeft(0)
              setIsActive(false)
              logout()
              return
            }

            setTimeLeft(remaining)

            // Show warning if less than warning time remaining
            if (remaining <= warningTimeMs && !showWarning) {
              setShowWarning(true)
            }
          }

          // Update immediately
          updateTimer()

          // Update every second
          const interval = setInterval(updateTimer, 1000)

          return () => clearInterval(interval)
        }
      } catch (error) {
        console.error('Session check error:', error)
        logout()
      }
    }

    checkSession()
  }, [warningTimeMs, showWarning, logout])

  // Reset timer on user activity
  useEffect(() => {
    const resetTimer = () => {
      if (isActive) {
        const now = Date.now()
        const expiryTime = now + sessionTimeoutMs
        localStorage.setItem('sessionExpiry', expiryTime.toString())
        setShowWarning(false)
      }
    }

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    events.forEach(event => {
      document.addEventListener(event, resetTimer, true)
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, resetTimer, true)
      })
    }
  }, [isActive, sessionTimeoutMs])

  // Don't render anything if session is not active or no warning needed
  if (!isActive || !showWarning) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <Card className="p-4 bg-yellow-50 border-yellow-200 shadow-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-apercu-bold text-sm text-yellow-800 mb-1">
              Session Expiring Soon
            </h3>
            <p className="font-apercu-regular text-xs text-yellow-700 mb-3">
              Your session will expire in:
            </p>
            <div className="flex items-center space-x-2 mb-3">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span className="font-apercu-bold text-sm text-yellow-800">
                {formatTime(timeLeft)}
              </span>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={extendSession}
                size="sm"
                className="font-apercu-medium text-xs bg-yellow-600 hover:bg-yellow-700 text-white"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Extend Session
              </Button>
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
                className="font-apercu-medium text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Logout Now
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
