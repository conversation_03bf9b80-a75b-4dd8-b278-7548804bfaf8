'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useNotifications } from '@/contexts/NotificationContext'
import { useMessages } from '@/contexts/MessageContext'
import { useUser } from '@/contexts/UserContext'
import { EnhancedSidebarLink, useOptimizedNavigation, useRoutePreloading } from './ClientSideNavigation'
import { useEffect } from 'react'
import { useSettings } from '@/hooks/useSettings'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

import {
  LayoutDashboard,
  Users,
  UserPlus,
  Settings,
  LogOut,
  BarChart3,
  FileText,
  Calendar,
  Bell,
  Shield,
  Mail,
  MessageSquare,
  Home
} from 'lucide-react'

interface SidebarProps {
  className?: string
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: LayoutDashboard,
    badge: null,
    requiredRoles: [], // Available to all roles
  },
  {
    name: 'Registrations',
    href: '/admin/registrations',
    icon: Users,
    badge: 'New',
    requiredRoles: [], // Available to all roles
  },
  {
    name: 'Accommodations',
    href: '/admin/accommodations',
    icon: Home,
    badge: null,
    requiredRoles: ['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer'], // All roles can access
  },
  {
    name: 'Communications',
    href: '/admin/communications',
    icon: Mail,
    badge: null,
    requiredRoles: ['Super Admin', 'Admin', 'Manager', 'Staff'], // Super Admin, Admin, Manager, and Staff
  },
  {
    name: 'Inbox',
    href: '/admin/inbox',
    icon: MessageSquare,
    badge: null,
    requiredRoles: [], // Available to all roles
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    badge: null,
    requiredRoles: [], // Available to all roles including Staff
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: FileText,
    badge: null,
    requiredRoles: [], // Available to all roles including Staff (view only)
  },
  {
    name: 'Events',
    href: '/admin/events',
    icon: Calendar,
    badge: null,
    requiredRoles: [], // Available to all roles
  },
  {
    name: 'Notifications',
    href: '/admin/notifications',
    icon: Bell,
    badge: '3',
    requiredRoles: [], // Available to all roles
  },
  {
    name: 'User Management',
    href: '/admin/users',
    icon: Shield,
    badge: null,
    requiredRoles: ['Super Admin', 'Admin', 'Manager'], // Super Admin, Admin, and Manager
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    badge: null,
    requiredRoles: ['Super Admin', 'Admin'], // Only Super Admin and Admin
  },
]

export function AdminSidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const { stats } = useNotifications()
  const { stats: messageStats } = useMessages()
  const { currentUser, loading } = useUser()
  const { getSystemName } = useSettings()
  const systemName = getSystemName()
  const { preloadCriticalRoutes } = useRoutePreloading()

  // Preload critical routes on component mount for faster navigation
  useEffect(() => {
    preloadCriticalRoutes()
  }, [])

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      window.location.href = '/admin/login'
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    if (!currentUser) return []

    return navigation.filter(item => {
      // If no required roles specified, show to everyone
      if (item.requiredRoles.length === 0) return true

      // Check if user's role is in the required roles
      const userRole = currentUser.role?.name || ''
      return item.requiredRoles.includes(userRole as any)
    })
  }

  // Create dynamic navigation with message count and role filtering
  const dynamicNavigation = getFilteredNavigation().map(item => {
    if (item.name === 'Inbox') {
      return {
        ...item,
        badge: messageStats.unread > 0 ? messageStats.unread.toString() : null
      }
    }
    if (item.name === 'Notifications') {
      return {
        ...item,
        badge: stats.unread > 0 ? stats.unread.toString() : null
      }
    }
    return item
  })

  // Generate user initials
  const getUserInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (loading) {
    return (
      <div className={cn('flex h-full w-64 flex-col bg-white border-r border-gray-200', className)}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex h-full w-64 flex-col bg-white border-r border-gray-200', className)}>
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
            <UserPlus className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="font-apercu-bold text-lg text-gray-900">{systemName}</h1>
            <p className="font-apercu-regular text-xs text-gray-500">Admin Panel</p>
          </div>
        </div>
      </div>

      {/* Navigation - Enhanced with Client-Side Routing */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {dynamicNavigation.map((item) => {
          const isActive = pathname === item.href

          // Get badge text with unread counts
          let badgeText = item.badge
          if (item.href === '/admin/inbox' && messageStats.unread > 0) {
            badgeText = messageStats.unread.toString()
          } else if (item.href === '/admin/notifications' && stats.unread > 0) {
            badgeText = stats.unread.toString()
          }

          return (
            <EnhancedSidebarLink
              key={item.name}
              href={item.href}
              icon={item.icon}
              isActive={isActive}
              badge={badgeText}
            >
              {item.name}
            </EnhancedSidebarLink>
          )
        })}
      </nav>

      <Separator className="mx-4" />

      {/* User section */}
      <div className="p-4">
        <div className="flex items-center space-x-3 mb-4">
          <div className="h-10 w-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white font-apercu-bold text-sm">
              {currentUser ? getUserInitials(currentUser.name) : 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="font-apercu-medium text-sm text-gray-900 truncate">
              {currentUser?.name || 'Unknown User'}
            </p>
            <p className="font-apercu-regular text-xs text-gray-500 truncate">
              {currentUser?.email || 'No email'}
            </p>
            {currentUser?.role && (
              <p className="font-apercu-regular text-xs text-indigo-600 truncate">
                {currentUser.role.name}
              </p>
            )}
          </div>
        </div>

        <Button
          onClick={handleLogout}
          variant="outline"
          size="sm"
          className="w-full font-apercu-medium"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
