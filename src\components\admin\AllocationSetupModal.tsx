'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/toast'
import { parseApiError } from '@/lib/error-messages'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Loader2, Shuffle, Users, Info } from 'lucide-react'

interface AllocationSetupModalProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (result: any) => void
  unallocatedCount: number
}

interface AllocationResult {
  group: string
  count: number
  allocated?: number
  remaining?: number
  status: 'success' | 'partial' | 'failed'
  reason?: string
}

export function AllocationSetupModal({ isOpen, onClose, onComplete, unallocatedCount }: AllocationSetupModalProps) {
  const [ageRangeYears, setAgeRangeYears] = useState('5')
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<AllocationResult[]>([])
  const [showResults, setShowResults] = useState(false)

  const { addToast } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({ title, type })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const ageRange = parseInt(ageRangeYears)
    if (isNaN(ageRange) || ageRange < 1) {
      showToast('Age range must be a positive number', 'error')
      return
    }

    try {
      setLoading(true)
      setShowResults(false)

      const response = await fetch('/api/admin/accommodations/allocate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ageRangeYears: ageRange
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to allocate rooms')
      }

      const data = await response.json()
      setResults(data.allocationResults || [])
      setShowResults(true)

      if (data.totalAllocated > 0) {
        onComplete(data)
      } else {
        showToast('No allocations were made. Please check room availability.', 'warning')
      }
    } catch (error) {
      console.error('Error allocating rooms:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setShowResults(false)
      setResults([])
      onClose()
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50'
      case 'partial': return 'text-amber-600 bg-amber-50'
      case 'failed': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✓'
      case 'partial': return '⚠'
      case 'failed': return '✗'
      default: return '?'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
              <Shuffle className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle className="font-apercu-bold text-lg">
                Auto Allocate Rooms
              </DialogTitle>
              <DialogDescription className="font-apercu-regular">
                Automatically assign participants to rooms based on age groups and gender
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {!showResults ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Info Card */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-apercu-bold text-sm text-blue-900 mb-1">How Auto Allocation Works</h4>
                  <ul className="font-apercu-regular text-sm text-blue-800 space-y-1">
                    <li>• Participants are grouped by gender and age range</li>
                    <li>• Rooms are assigned based on gender matching</li>
                    <li>• Age groups help keep similar ages together</li>
                    <li>• Existing allocations are preserved</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Current Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-gray-600" />
                </div>
                <p className="font-apercu-bold text-xl text-gray-900">{unallocatedCount}</p>
                <p className="font-apercu-medium text-sm text-gray-600">Unallocated</p>
              </div>
              <div className="text-center p-4 bg-indigo-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Shuffle className="h-5 w-5 text-indigo-600" />
                </div>
                <p className="font-apercu-bold text-xl text-indigo-900">Auto</p>
                <p className="font-apercu-medium text-sm text-indigo-600">Allocation</p>
              </div>
            </div>

            {/* Age Range Input */}
            <div className="space-y-2">
              <Label htmlFor="ageRange" className="font-apercu-medium text-sm text-gray-700">
                Age Range for Grouping (Years) *
              </Label>
              <Input
                id="ageRange"
                type="number"
                min="1"
                max="20"
                value={ageRangeYears}
                onChange={(e) => setAgeRangeYears(e.target.value)}
                placeholder="e.g., 5"
                className="font-apercu-regular"
                disabled={loading}
              />
              <p className="font-apercu-regular text-xs text-gray-500">
                Participants will be grouped within {ageRangeYears || 'X'} year age ranges (e.g., 14-18, 19-23)
              </p>
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
                className="font-apercu-medium"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || unallocatedCount === 0}
                className="font-apercu-medium bg-purple-600 hover:bg-purple-700"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Allocating...
                  </>
                ) : (
                  <>
                    <Shuffle className="h-4 w-4 mr-2" />
                    Start Auto Allocation
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="space-y-6">
            {/* Results Header */}
            <div className="text-center">
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">Allocation Results</h3>
              <p className="font-apercu-regular text-sm text-gray-600">
                Age range: {ageRangeYears} years
              </p>
            </div>

            {/* Results List */}
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {results.map((result, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-apercu-bold text-sm text-gray-900">{result.group}</h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-apercu-medium ${getStatusColor(result.status)}`}>
                      {getStatusIcon(result.status)} {result.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div>
                      <p className="font-apercu-bold text-sm text-gray-900">{result.count}</p>
                      <p className="font-apercu-regular text-xs text-gray-500">Total</p>
                    </div>
                    <div>
                      <p className="font-apercu-bold text-sm text-green-600">{result.allocated || 0}</p>
                      <p className="font-apercu-regular text-xs text-gray-500">Allocated</p>
                    </div>
                    <div>
                      <p className="font-apercu-bold text-sm text-amber-600">{result.remaining || 0}</p>
                      <p className="font-apercu-regular text-xs text-gray-500">Remaining</p>
                    </div>
                  </div>

                  {result.reason && (
                    <p className="font-apercu-regular text-xs text-red-600 mt-2">{result.reason}</p>
                  )}
                </div>
              ))}
            </div>

            <DialogFooter>
              <Button
                onClick={handleClose}
                className="font-apercu-medium bg-indigo-600 hover:bg-indigo-700"
              >
                Done
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
