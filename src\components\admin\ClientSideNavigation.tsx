'use client'

import { useRouter, usePathname } from 'next/navigation'
import { useState, useTransition } from 'react'
import { cn } from '@/lib/utils'

interface ClientSideNavigationProps {
  href: string
  children: React.ReactNode
  className?: string
  onClick?: () => void
  prefetch?: boolean
}

export function ClientSideNavigation({ 
  href, 
  children, 
  className, 
  onClick,
  prefetch = true 
}: ClientSideNavigationProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isPending, startTransition] = useTransition()
  const [isHovered, setIsHovered] = useState(false)

  const isActive = pathname === href
  const isLoading = isPending && isActive

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    
    // Call custom onClick if provided
    onClick?.()
    
    // Don't navigate if already on the same page
    if (pathname === href) return
    
    // Use startTransition for smooth navigation
    startTransition(() => {
      router.push(href)
    })
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    // Prefetch the route on hover for faster navigation
    if (prefetch && href !== pathname) {
      router.prefetch(href)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  return (
    <a
      href={href}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={cn(
        'transition-all duration-200 relative',
        isLoading && 'opacity-70 pointer-events-none',
        className
      )}
      aria-current={isActive ? 'page' : undefined}
    >
      {children}
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 rounded-lg">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
        </div>
      )}
      
      {/* Hover effect */}
      {isHovered && !isActive && (
        <div className="absolute inset-0 bg-gray-50 rounded-lg opacity-50 pointer-events-none"></div>
      )}
    </a>
  )
}

// Enhanced sidebar navigation with client-side routing
interface EnhancedSidebarLinkProps {
  href: string
  icon: React.ComponentType<{ className?: string }>
  children: React.ReactNode
  badge?: string | null
  isActive?: boolean
  className?: string
}

export function EnhancedSidebarLink({
  href,
  icon: Icon,
  children,
  badge,
  isActive,
  className
}: EnhancedSidebarLinkProps) {
  const [isPending, startTransition] = useTransition()
  const router = useRouter()
  const pathname = usePathname()

  const handleNavigation = (e: React.MouseEvent) => {
    e.preventDefault()
    
    if (pathname === href) return
    
    startTransition(() => {
      router.push(href)
    })
  }

  return (
    <a
      href={href}
      onClick={handleNavigation}
      className={cn(
        'flex items-center justify-between px-3 py-2.5 text-sm font-apercu-medium rounded-lg transition-all duration-200 relative',
        isActive
          ? 'bg-indigo-50 text-indigo-700 border border-indigo-200'
          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900',
        isPending && 'opacity-70',
        className
      )}
    >
      <div className="flex items-center space-x-3">
        <Icon className={cn(
          'h-5 w-5 transition-colors duration-200',
          isActive ? 'text-indigo-600' : 'text-gray-400'
        )} />
        <span className="transition-colors duration-200">{children}</span>
      </div>
      
      {badge && (
        <span className={cn(
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-apercu-medium transition-colors duration-200',
          badge === 'New' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-blue-100 text-blue-800'
        )}>
          {badge}
        </span>
      )}
      
      {/* Loading indicator */}
      {isPending && (
        <div className="absolute right-2">
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-600"></div>
        </div>
      )}
    </a>
  )
}

// Page transition wrapper
interface PageTransitionProps {
  children: React.ReactNode
  className?: string
}

export function PageTransition({ children, className }: PageTransitionProps) {
  const [isPending] = useTransition()

  return (
    <div className={cn(
      'transition-all duration-300',
      isPending ? 'opacity-70 scale-[0.98]' : 'opacity-100 scale-100',
      className
    )}>
      {children}
      
      {/* Global loading overlay */}
      {isPending && (
        <div className="fixed inset-0 bg-white/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <span className="font-apercu-medium text-gray-700">Loading...</span>
          </div>
        </div>
      )}
    </div>
  )
}

// Optimized navigation hook
export function useOptimizedNavigation() {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()

  const navigateTo = (href: string, options?: { replace?: boolean }) => {
    startTransition(() => {
      if (options?.replace) {
        router.replace(href)
      } else {
        router.push(href)
      }
    })
  }

  const prefetchRoute = (href: string) => {
    router.prefetch(href)
  }

  return {
    navigateTo,
    prefetchRoute,
    isPending
  }
}

// Performance monitoring for navigation
export function useNavigationPerformance() {
  const pathname = usePathname()
  const [navigationTimes, setNavigationTimes] = useState<Record<string, number>>({})

  const measureNavigation = (targetPath: string) => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      setNavigationTimes(prev => ({
        ...prev,
        [targetPath]: duration
      }))
      
      // Log slow navigations
      if (duration > 1000) {
        console.warn(`🐌 Slow navigation to ${targetPath}: ${duration.toFixed(2)}ms`)
      }
      
      // Send to analytics if available
      if (typeof window !== 'undefined' && 'gtag' in window) {
        (window as any).gtag('event', 'page_navigation', {
          page_path: targetPath,
          navigation_time: Math.round(duration)
        })
      }
    }
  }

  return {
    navigationTimes,
    measureNavigation,
    currentPath: pathname
  }
}

// Preload critical routes
export function useRoutePreloading() {
  const router = useRouter()

  const preloadCriticalRoutes = () => {
    const criticalRoutes = [
      '/admin/dashboard',
      '/admin/registrations',
      '/admin/accommodations',
      '/admin/inbox',
      '/admin/analytics'
    ]

    criticalRoutes.forEach(route => {
      router.prefetch(route)
    })
  }

  return { preloadCriticalRoutes }
}
