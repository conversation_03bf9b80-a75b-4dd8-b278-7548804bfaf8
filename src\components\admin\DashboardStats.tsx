'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  UserPlus, 
  TrendingUp, 
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity
} from 'lucide-react'

interface StatsCardProps {
  title: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon: React.ElementType
  description?: string
}

function StatsCard({ title, value, change, changeType = 'neutral', icon: Icon, description }: StatsCardProps) {
  const changeColor = {
    positive: 'text-green-600 bg-green-50',
    negative: 'text-red-600 bg-red-50',
    neutral: 'text-gray-600 bg-gray-50'
  }[changeType]

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="font-apercu-medium text-sm text-gray-600 mb-1">{title}</p>
          <p className="font-apercu-bold text-3xl text-gray-900 mb-2">{value}</p>
          {description && (
            <p className="font-apercu-regular text-xs text-gray-500 mb-2">{description}</p>
          )}
          {change && (
            <div className="flex items-center space-x-1">
              <Badge className={`${changeColor} border-0 font-apercu-medium text-xs px-2 py-1`}>
                {change}
              </Badge>
              <span className="font-apercu-regular text-xs text-gray-500">vs last month</span>
            </div>
          )}
        </div>
        <div className="ml-4">
          <div className="h-12 w-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </div>
    </Card>
  )
}

interface DashboardStatsProps {
  stats: {
    totalRegistrations: number
    newRegistrations: number
    completedRegistrations: number
    pendingRegistrations: number
    recentActivity: number
  }
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const completionRate = stats.totalRegistrations > 0 
    ? Math.round((stats.completedRegistrations / stats.totalRegistrations) * 100)
    : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="Total Registrations"
        value={stats.totalRegistrations}
        change="+12%"
        changeType="positive"
        icon={Users}
        description="All time registrations"
      />
      
      <StatsCard
        title="New This Month"
        value={stats.newRegistrations}
        change="+8%"
        changeType="positive"
        icon={UserPlus}
        description="Recent registrations"
      />
      
      <StatsCard
        title="Completion Rate"
        value={`${completionRate}%`}
        change="+5%"
        changeType="positive"
        icon={CheckCircle}
        description="Successfully completed"
      />
      
      <StatsCard
        title="Recent Activity"
        value={stats.recentActivity}
        change="Live"
        changeType="neutral"
        icon={Activity}
        description="Last 24 hours"
      />
    </div>
  )
}
