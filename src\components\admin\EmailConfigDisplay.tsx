'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Mail, 
  Server, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Copy,
  Eye,
  EyeOff
} from 'lucide-react'

interface EmailConfig {
  fromName: string
  fromEmail: string
  replyTo: string
  smtpHost: string
  smtpPort: string
  isSecure: boolean
  isConfigured: boolean
  environment: string
}

export function EmailConfigDisplay() {
  const [emailConfig, setEmailConfig] = useState<EmailConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchEmailConfig()
  }, [])

  const fetchEmailConfig = async () => {
    try {
      const response = await fetch('/api/admin/email-config')
      if (response.ok) {
        const data = await response.json()
        setEmailConfig(data.config)
      }
    } catch (error) {
      console.error('Failed to fetch email config:', error)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  if (loading) {
    return (
      <Card className="p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </Card>
    )
  }

  if (!emailConfig) {
    return (
      <Card className="p-4 border-red-200 bg-red-50">
        <div className="flex items-center space-x-2">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <span className="font-apercu-medium text-sm text-red-800">
            Email configuration unavailable
          </span>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Mail className="h-5 w-5 text-indigo-600" />
          <h3 className="font-apercu-bold text-sm text-gray-900">Email Configuration</h3>
          <Badge 
            variant={emailConfig.isConfigured ? 'success' : 'destructive'}
            className="font-apercu-medium text-xs"
          >
            {emailConfig.isConfigured ? 'Active' : 'Not Configured'}
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="font-apercu-medium text-xs"
        >
          {showDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          {showDetails ? 'Hide' : 'Show'} Details
        </Button>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="font-apercu-regular text-xs text-gray-600">From Email:</span>
          <div className="flex items-center space-x-1">
            <span className="font-apercu-medium text-xs text-gray-900">
              {emailConfig.fromEmail}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(emailConfig.fromEmail)}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="font-apercu-regular text-xs text-gray-600">From Name:</span>
          <span className="font-apercu-medium text-xs text-gray-900">
            {emailConfig.fromName}
          </span>
        </div>

        {showDetails && (
          <>
            <div className="flex items-center justify-between">
              <span className="font-apercu-regular text-xs text-gray-600">Reply To:</span>
              <span className="font-apercu-medium text-xs text-gray-900">
                {emailConfig.replyTo}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="font-apercu-regular text-xs text-gray-600">SMTP Host:</span>
              <span className="font-apercu-medium text-xs text-gray-900">
                {emailConfig.smtpHost}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="font-apercu-regular text-xs text-gray-600">SMTP Port:</span>
              <div className="flex items-center space-x-1">
                <span className="font-apercu-medium text-xs text-gray-900">
                  {emailConfig.smtpPort}
                </span>
                {emailConfig.isSecure && (
                  <Shield className="h-3 w-3 text-green-600" title="Secure Connection" />
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="font-apercu-regular text-xs text-gray-600">Environment:</span>
              <Badge 
                variant={emailConfig.environment === 'production' ? 'default' : 'secondary'}
                className="font-apercu-medium text-xs"
              >
                {emailConfig.environment}
              </Badge>
            </div>
          </>
        )}
      </div>

      {emailConfig.isConfigured && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="font-apercu-regular text-xs text-green-800">
              Ready to send emails to registrants
            </span>
          </div>
        </div>
      )}
    </Card>
  )
}
