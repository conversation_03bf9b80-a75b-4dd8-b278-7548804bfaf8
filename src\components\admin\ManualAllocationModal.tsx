'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { UserPlus, Search, Users, Home, AlertTriangle, CheckCircle, X } from 'lucide-react'

interface Registration {
  id: string
  fullName: string
  gender: string
  dateOfBirth: string
  phoneNumber: string
  emailAddress: string
}

interface Room {
  id: string
  name: string
  gender: string
  capacity: number
  occupancy: number
  availableSpaces: number
  allocations: Array<{
    id: string
    registration: {
      id: string
      fullName: string
      dateOfBirth: string
    }
  }>
}

interface ManualAllocationModalProps {
  isOpen: boolean
  onClose: () => void
  onComplete: (result: any) => void
  maxAgeDifference?: number
}

export function ManualAllocationModal({ 
  isOpen, 
  onClose, 
  onComplete, 
  maxAgeDifference = 5 
}: ManualAllocationModalProps) {
  const [unallocatedRegistrations, setUnallocatedRegistrations] = useState<Registration[]>([])
  const [availableRooms, setAvailableRooms] = useState<Room[]>([])
  const [selectedRegistration, setSelectedRegistration] = useState<Registration | null>(null)
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(false)
  const [allocating, setAllocating] = useState(false)
  const [ageWarning, setAgeWarning] = useState<string | null>(null)

  const { addToast } = useToast()

  useEffect(() => {
    if (isOpen) {
      fetchData()
    }
  }, [isOpen])

  useEffect(() => {
    if (selectedRegistration && selectedRoom) {
      checkAgeCompatibility()
    } else {
      setAgeWarning(null)
    }
  }, [selectedRegistration, selectedRoom])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch all registrations and filter unallocated ones
      const regResponse = await fetch('/api/registrations?limit=1000')
      if (regResponse.ok) {
        const regData = await regResponse.json()
        // Filter for unallocated registrations (those without room allocation)
        const unallocated = regData.registrations?.filter((reg: any) => !reg.roomAllocation) || []
        setUnallocatedRegistrations(unallocated)
      }

      // Fetch available rooms
      const roomResponse = await fetch('/api/admin/accommodations')
      if (roomResponse.ok) {
        const roomData = await roomResponse.json()
        const allRooms = [
          ...(roomData.roomsByGender?.Male || []),
          ...(roomData.roomsByGender?.Female || [])
        ]
        setAvailableRooms(allRooms.filter(room => room.availableSpaces > 0))
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      addToast({
        type: 'error',
        title: 'Error',
        description: 'Failed to load allocation data'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const checkAgeCompatibility = () => {
    if (!selectedRegistration || !selectedRoom) return

    const newAge = calculateAge(selectedRegistration.dateOfBirth)
    
    if (selectedRoom.allocations.length === 0) {
      setAgeWarning(null)
      return
    }

    const existingAges = selectedRoom.allocations.map(alloc => 
      calculateAge(alloc.registration.dateOfBirth)
    )
    
    const allAges = [...existingAges, newAge]
    const minAge = Math.min(...allAges)
    const maxAge = Math.max(...allAges)
    const ageDifference = maxAge - minAge

    if (ageDifference > maxAgeDifference) {
      setAgeWarning(
        `Age difference would be ${ageDifference} years (${minAge}-${maxAge}), exceeding the ${maxAgeDifference}-year limit.`
      )
    } else {
      setAgeWarning(null)
    }
  }

  const handleAllocate = async () => {
    if (!selectedRegistration || !selectedRoom) return

    try {
      setAllocating(true)
      
      const response = await fetch('/api/admin/accommodations/manual-allocate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          registrationId: selectedRegistration.id,
          roomId: selectedRoom.id,
          maxAgeDifference
        })
      })

      if (response.ok) {
        const result = await response.json()
        addToast({
          type: 'success',
          title: 'Allocation Successful',
          description: result.message
        })
        onComplete(result)
        handleClose()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to allocate')
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Allocation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setAllocating(false)
    }
  }

  const handleClose = () => {
    setSelectedRegistration(null)
    setSelectedRoom(null)
    setSearchTerm('')
    setAgeWarning(null)
    onClose()
  }

  const filteredRegistrations = unallocatedRegistrations.filter(reg =>
    reg.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reg.emailAddress.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const compatibleRooms = selectedRegistration 
    ? availableRooms.filter(room => room.gender === selectedRegistration.gender)
    : []

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle className="font-apercu-bold text-lg">
                Manual Room Allocation
              </DialogTitle>
              <DialogDescription className="font-apercu-regular">
                Manually assign participants to specific rooms with age validation
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-y-auto max-h-[60vh]">
          {/* Participant Selection */}
          <div className="space-y-4">
            <div>
              <Label className="font-apercu-medium text-sm text-gray-700 mb-2 block">
                Select Participant
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search participants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 font-apercu-regular"
                />
              </div>
            </div>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {filteredRegistrations.map((registration) => (
                <Card
                  key={registration.id}
                  className={`p-3 cursor-pointer transition-colors ${
                    selectedRegistration?.id === registration.id
                      ? 'bg-green-50 border-green-200'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedRegistration(registration)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-apercu-medium text-sm text-gray-900">
                        {registration.fullName}
                      </p>
                      <p className="font-apercu-regular text-xs text-gray-500">
                        {registration.gender} • {calculateAge(registration.dateOfBirth)} years old
                      </p>
                    </div>
                    <Badge variant={registration.gender === 'Male' ? 'blue' : 'pink'}>
                      {registration.gender}
                    </Badge>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Room Selection */}
          <div className="space-y-4">
            <Label className="font-apercu-medium text-sm text-gray-700">
              Select Room {selectedRegistration && `(${selectedRegistration.gender} only)`}
            </Label>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {compatibleRooms.map((room) => {
                const roomAges = room.allocations.map(alloc => 
                  calculateAge(alloc.registration.dateOfBirth)
                )
                const ageRange = roomAges.length > 0 
                  ? `${Math.min(...roomAges)}-${Math.max(...roomAges)} years`
                  : 'No occupants'

                return (
                  <Card
                    key={room.id}
                    className={`p-3 cursor-pointer transition-colors ${
                      selectedRoom?.id === room.id
                        ? 'bg-blue-50 border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedRoom(room)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Home className="h-4 w-4 text-gray-500" />
                        <p className="font-apercu-medium text-sm text-gray-900">
                          {room.name}
                        </p>
                      </div>
                      <Badge variant="outline">
                        {room.occupancy}/{room.capacity}
                      </Badge>
                    </div>
                    <p className="font-apercu-regular text-xs text-gray-500">
                      Age range: {ageRange}
                    </p>
                  </Card>
                )
              })}
            </div>
          </div>
        </div>

        {/* Age Warning */}
        {ageWarning && (
          <div className="flex items-start space-x-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <p className="font-apercu-medium text-sm text-amber-800">Age Compatibility Warning</p>
              <p className="font-apercu-regular text-xs text-amber-700">{ageWarning}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={allocating}
            className="font-apercu-medium"
          >
            Cancel
          </Button>
          <Button
            onClick={handleAllocate}
            disabled={!selectedRegistration || !selectedRoom || allocating || !!ageWarning}
            className="font-apercu-medium"
          >
            {allocating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Allocating...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Allocate to Room
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
