'use client'

import { useEffect, useState } from 'react'
import { MessageThreadView } from './MessageThreadView'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  subject: string
  content: string
  senderEmail: string
  senderName: string
  recipientEmail: string
  recipientName: string
  senderType: 'admin' | 'user'
  recipientType: 'admin' | 'user'
  sentAt: string
  readAt: string | null
  threadId?: string
  parentId?: string
  threadMessages?: Message[]
}

interface MessageModalProps {
  isOpen: boolean
  onClose: () => void
  message: Message | null
  currentUserEmail: string
  onReply?: (content: string) => void
}

export function MessageModal({ 
  isOpen, 
  onClose, 
  message, 
  currentUserEmail, 
  onReply 
}: MessageModalProps) {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen || !message) return null

  if (isMobile) {
    // Mobile: Full screen modal
    return (
      <div className="fixed inset-0 z-50 bg-white">
        <MessageThreadView
          message={message}
          currentUserEmail={currentUserEmail}
          onClose={onClose}
          onReply={onReply}
          isMobile={true}
        />
      </div>
    )
  }

  // Desktop: Overlay modal
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative w-full max-w-4xl max-h-[90vh] mx-4">
        <MessageThreadView
          message={message}
          currentUserEmail={currentUserEmail}
          onClose={onClose}
          onReply={onReply}
          isMobile={false}
        />
      </div>
    </div>
  )
}

// Enhanced Message List Item for better thread display
interface MessageListItemProps {
  message: Message
  currentUserEmail: string
  onClick: () => void
  isSelected?: boolean
}

export function MessageListItem({ 
  message, 
  currentUserEmail, 
  onClick, 
  isSelected = false 
}: MessageListItemProps) {
  const getInitials = (name: string) => {
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  const isCurrentUser = message.senderEmail === currentUserEmail
  const isUnread = !message.readAt && !isCurrentUser

  return (
    <div
      onClick={onClick}
      className={cn(
        "p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors duration-200",
        isSelected && "bg-indigo-50 border-indigo-200",
        isUnread && "bg-blue-50"
      )}
    >
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className={cn(
            "h-10 w-10 rounded-full flex items-center justify-center",
            isCurrentUser 
              ? "bg-gradient-to-br from-indigo-500 to-purple-600"
              : "bg-gradient-to-br from-green-500 to-blue-600"
          )}>
            <span className="text-white font-apercu-bold text-sm">
              {getInitials(message.senderName)}
            </span>
          </div>
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-2">
              <p className={cn(
                "font-apercu-bold text-sm truncate",
                isUnread ? "text-gray-900" : "text-gray-700"
              )}>
                {message.senderName}
              </p>
              {isCurrentUser && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  You
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <p className="font-apercu-regular text-xs text-gray-500">
                {formatTime(message.sentAt)}
              </p>
              {isUnread && (
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className={cn(
              "font-apercu-medium text-sm truncate flex-1",
              isUnread ? "text-gray-900" : "text-gray-600"
            )}>
              {message.subject}
            </p>
            
            {/* Thread indicators */}
            <div className="flex items-center space-x-1 ml-2">
              {message.threadId && (
                <div className="flex items-center space-x-1">
                  <div className="h-4 w-4 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-apercu-bold text-indigo-600">
                      {message.threadMessages?.length || 1}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <p className={cn(
            "font-apercu-regular text-xs mt-1 line-clamp-2",
            isUnread ? "text-gray-700" : "text-gray-500"
          )}>
            {message.content}
          </p>
        </div>
      </div>
    </div>
  )
}

// Hook for responsive design
export function useResponsiveDesign() {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }
    
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  return { isMobile, isTablet, isDesktop: !isMobile && !isTablet }
}
