'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/toast'
import { parseApiError } from '@/lib/error-messages'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Mail,
  Send,
  TestTube,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface MessageTestModalProps {
  isOpen: boolean
  onClose: () => void
}

export function MessageTestModal({ isOpen, onClose }: MessageTestModalProps) {
  const [testType, setTestType] = useState<'email' | 'message'>('email')
  const [testEmail, setTestEmail] = useState('')
  const [recipientEmail, setRecipientEmail] = useState('')
  const [recipientType, setRecipientType] = useState<'admin' | 'user'>('admin')
  const [subject, setSubject] = useState('Test Message')
  const [message, setMessage] = useState('This is a test message to verify the messaging system is working correctly.')
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState<any>(null)

  const { addToast } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({ title, type })
  }

  const testEmailConfiguration = async () => {
    if (!testEmail.trim()) {
      showToast('Please enter a test email address', 'error')
      return
    }

    try {
      setTesting(true)
      setResult(null)

      const response = await fetch('/api/admin/messages/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          testEmail: testEmail.trim()
        })
      })

      const data = await response.json()
      setResult(data)

      if (response.ok) {
        showToast('Test email sent successfully!', 'success')
      } else {
        showToast(`Test failed: ${data.error}`, 'error')
      }
    } catch (error) {
      console.error('Email test error:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
      setResult({ error: errorMessage.description })
    } finally {
      setTesting(false)
    }
  }

  const testMessageSending = async () => {
    if (!recipientEmail.trim() || !subject.trim() || !message.trim()) {
      showToast('Please fill in all fields', 'error')
      return
    }

    try {
      setTesting(true)
      setResult(null)

      const response = await fetch('/api/admin/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          recipientId: recipientEmail.trim(),
          recipientType,
          subject: subject.trim(),
          message: message.trim()
        })
      })

      const data = await response.json()
      setResult(data)

      if (response.ok) {
        showToast('Test message sent successfully!', 'success')
      } else {
        showToast(`Message failed: ${data.error}`, 'error')
      }
    } catch (error) {
      console.error('Message test error:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
      setResult({ error: errorMessage.description })
    } finally {
      setTesting(false)
    }
  }

  const handleTest = () => {
    if (testType === 'email') {
      testEmailConfiguration()
    } else {
      testMessageSending()
    }
  }

  const resetForm = () => {
    setTestEmail('')
    setRecipientEmail('')
    setSubject('Test Message')
    setMessage('This is a test message to verify the messaging system is working correctly.')
    setResult(null)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <TestTube className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle className="font-apercu-bold text-lg">
                Message System Test
              </DialogTitle>
              <DialogDescription className="font-apercu-regular text-sm">
                Test email configuration and message sending functionality
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Test Type Selection */}
          <div>
            <Label className="font-apercu-medium text-sm">Test Type</Label>
            <Select value={testType} onValueChange={(value: 'email' | 'message') => setTestType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="email">Email Configuration Test</SelectItem>
                <SelectItem value="message">Message Sending Test</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Email Configuration Test */}
          {testType === 'email' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="testEmail" className="font-apercu-medium text-sm">
                  Test Email Address *
                </Label>
                <Input
                  id="testEmail"
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="Enter email to receive test message"
                  className="font-apercu-regular"
                />
                <p className="text-xs text-gray-500 mt-1">
                  A test email will be sent to this address to verify SMTP configuration
                </p>
              </div>
            </div>
          )}

          {/* Message Sending Test */}
          {testType === 'message' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="recipientEmail" className="font-apercu-medium text-sm">
                    Recipient Email *
                  </Label>
                  <Input
                    id="recipientEmail"
                    type="email"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="font-apercu-regular"
                  />
                </div>
                <div>
                  <Label className="font-apercu-medium text-sm">Recipient Type *</Label>
                  <Select value={recipientType} onValueChange={(value: 'admin' | 'user') => setRecipientType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="subject" className="font-apercu-medium text-sm">
                  Subject *
                </Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Message subject"
                  className="font-apercu-regular"
                />
              </div>

              <div>
                <Label htmlFor="message" className="font-apercu-medium text-sm">
                  Message *
                </Label>
                <Textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Message content"
                  className="font-apercu-regular"
                  rows={4}
                />
              </div>
            </div>
          )}

          {/* Test Result */}
          {result && (
            <div className={`p-4 rounded-lg border ${
              result.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                <h4 className={`font-apercu-bold text-sm ${
                  result.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {result.success ? 'Test Successful' : 'Test Failed'}
                </h4>
              </div>
              
              <p className={`font-apercu-regular text-sm ${
                result.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {result.message || result.error}
              </p>

              {result.details && (
                <p className="font-apercu-regular text-xs text-gray-600 mt-2">
                  Details: {result.details}
                </p>
              )}

              {result.emailConfig && (
                <div className="mt-3 p-3 bg-gray-100 rounded text-xs">
                  <h5 className="font-apercu-bold text-gray-900 mb-1">Email Configuration:</h5>
                  <pre className="font-mono text-gray-700">
                    {JSON.stringify(result.emailConfig, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button
            onClick={resetForm}
            variant="outline"
            className="font-apercu-medium"
          >
            Reset
          </Button>
          
          <div className="flex space-x-2">
            <Button
              onClick={onClose}
              variant="outline"
              className="font-apercu-medium"
            >
              Close
            </Button>
            <Button
              onClick={handleTest}
              disabled={testing}
              className="font-apercu-medium"
            >
              {testing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Testing...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Run Test
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
