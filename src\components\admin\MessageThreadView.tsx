'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Send, 
  X, 
  MessageSquare, 
  Clock,
  Check,
  CheckCheck
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/components/ui/toast'
import { useIsClient } from '@/components/ClientOnly'

interface Message {
  id: string
  subject: string
  content: string
  senderEmail: string
  senderName: string
  recipientEmail: string
  recipientName: string
  senderType: 'admin' | 'user'
  recipientType: 'admin' | 'user'
  sentAt: string
  readAt: string | null
  threadId?: string
  parentId?: string
  threadMessages?: Message[]
}

interface MessageThreadViewProps {
  message: Message
  currentUserEmail: string
  onClose: () => void
  onReply?: (content: string) => void
  isMobile?: boolean
}

export function MessageThreadView({ 
  message, 
  currentUserEmail, 
  onClose, 
  onReply,
  isMobile = false 
}: MessageThreadViewProps) {
  const [replyContent, setReplyContent] = useState('')
  const [isReplying, setIsReplying] = useState(false)
  const [threadMessages, setThreadMessages] = useState<Message[]>([])
  const { addToast } = useToast()

  // Get all messages in the thread
  useEffect(() => {
    if (message.threadId) {
      fetchThreadMessages()
    } else {
      // If no thread, just show the current message
      setThreadMessages([message])
    }
  }, [message])

  const fetchThreadMessages = async () => {
    try {
      const response = await fetch(`/api/admin/messages/thread/${message.threadId}`)
      if (response.ok) {
        const data = await response.json()
        setThreadMessages(data.messages || [])
      }
    } catch (error) {
      console.error('Failed to fetch thread messages:', error)
      setThreadMessages([message])
    }
  }

  const handleReply = async () => {
    if (!replyContent.trim()) return

    setIsReplying(true)
    try {
      const response = await fetch(`/api/admin/messages/${message.id}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: replyContent
        })
      })

      if (response.ok) {
        addToast({
          type: 'success',
          title: 'Reply Sent',
          description: 'Your reply has been sent successfully.',
          duration: 3000
        })
        setReplyContent('')
        onReply?.(replyContent)
        fetchThreadMessages() // Refresh thread
      } else {
        throw new Error('Failed to send reply')
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Reply Failed',
        description: 'Unable to send your reply. Please try again.',
        duration: 5000
      })
    } finally {
      setIsReplying(false)
    }
  }

  const isClient = useIsClient()

  // Use safe date formatting to prevent hydration mismatches
  const formatTime = (dateString: string) => {
    if (!isClient) {
      // Return a simple fallback for SSR
      const date = new Date(dateString)
      const hours = date.getUTCHours()
      const minutes = date.getUTCMinutes()
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    }

    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (dateString: string) => {
    if (!isClient) {
      // Return a simple fallback for SSR
      const date = new Date(dateString)
      return date.toISOString().split('T')[0]
    }

    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
  }

  const isCurrentUser = (senderEmail: string) => senderEmail === currentUserEmail

  const getMessageStatus = (msg: Message) => {
    if (isCurrentUser(msg.senderEmail)) {
      return msg.readAt ? 'read' : 'delivered'
    }
    return null
  }

  // Sort messages chronologically
  const sortedMessages = [...threadMessages].sort((a, b) => 
    new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime()
  )

  const parentMessage = sortedMessages[0]
  const replies = sortedMessages.slice(1)

  const containerClass = isMobile 
    ? "fixed inset-0 bg-white z-50 flex flex-col"
    : "w-full max-w-2xl mx-auto bg-white rounded-lg shadow-lg border border-gray-200"

  return (
    <div className={containerClass}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-indigo-600" />
            <div>
              <h3 className="font-apercu-bold text-lg text-gray-900">Thread</h3>
              <p className="font-apercu-regular text-sm text-indigo-600">
                {parentMessage?.subject || message.subject}
              </p>
            </div>
          </div>
        </div>
        {replies.length > 0 && (
          <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">
            {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
          </Badge>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Parent Message */}
        {parentMessage && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8 bg-gradient-to-br from-indigo-500 to-purple-600">
                <span className="text-white font-apercu-bold text-xs">
                  {getInitials(parentMessage.senderName)}
                </span>
              </Avatar>
              <div>
                <p className="font-apercu-bold text-sm text-gray-900">
                  {parentMessage.senderName}
                </p>
                <p className="font-apercu-regular text-xs text-gray-500">
                  {formatDate(parentMessage.sentAt)} at {formatTime(parentMessage.sentAt)}
                </p>
              </div>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg border",
              isCurrentUser(parentMessage.senderEmail)
                ? "bg-indigo-50 border-indigo-200 ml-8"
                : "bg-gray-50 border-gray-200"
            )}>
              <p className="font-apercu-regular text-gray-900 whitespace-pre-wrap">
                {parentMessage.content}
              </p>
              <div className="flex items-center justify-between mt-2">
                <p className="font-apercu-regular text-xs text-gray-500">
                  {formatTime(parentMessage.sentAt)}
                </p>
                {getMessageStatus(parentMessage) && (
                  <div className="flex items-center space-x-1">
                    {getMessageStatus(parentMessage) === 'read' ? (
                      <CheckCheck className="h-3 w-3 text-blue-500" />
                    ) : (
                      <Check className="h-3 w-3 text-gray-400" />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Replies */}
        {replies.map((reply, index) => (
          <div key={reply.id} className="space-y-3">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8 bg-gradient-to-br from-green-500 to-blue-600">
                <span className="text-white font-apercu-bold text-xs">
                  {getInitials(reply.senderName)}
                </span>
              </Avatar>
              <div>
                <p className="font-apercu-bold text-sm text-gray-900">
                  {reply.senderName}
                </p>
                <p className="font-apercu-regular text-xs text-gray-500">
                  {formatDate(reply.sentAt)} at {formatTime(reply.sentAt)}
                </p>
              </div>
            </div>
            
            <div className={cn(
              "p-4 rounded-lg border",
              isCurrentUser(reply.senderEmail)
                ? "bg-indigo-50 border-indigo-200 ml-8"
                : "bg-gray-50 border-gray-200"
            )}>
              <p className="font-apercu-regular text-gray-900 whitespace-pre-wrap">
                {reply.content}
              </p>
              <div className="flex items-center justify-between mt-2">
                <p className="font-apercu-regular text-xs text-gray-500">
                  {formatTime(reply.sentAt)}
                </p>
                {getMessageStatus(reply) && (
                  <div className="flex items-center space-x-1">
                    {getMessageStatus(reply) === 'read' ? (
                      <CheckCheck className="h-3 w-3 text-blue-500" />
                    ) : (
                      <Check className="h-3 w-3 text-gray-400" />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Reply Input */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="space-y-3">
          <textarea
            placeholder="Type your reply..."
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            className="w-full min-h-[80px] p-3 border border-gray-300 rounded-lg resize-none font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            disabled={isReplying}
          />
          <div className="flex justify-between items-center">
            <p className="font-apercu-regular text-xs text-gray-500">
              Press Enter to send
            </p>
            <Button
              onClick={handleReply}
              disabled={!replyContent.trim() || isReplying}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-apercu-medium"
            >
              {isReplying ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              {isReplying ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
