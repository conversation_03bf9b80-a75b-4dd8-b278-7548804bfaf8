'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { usePerformance, useApiPerformance, usePagePerformance, performanceUtils } from '@/hooks/usePerformance'
import { 
  Activity, 
  Zap, 
  Database, 
  Image as ImageIcon, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react'

export function PerformanceDashboard() {
  const { metrics } = usePerformance({ 
    componentName: 'PerformanceDashboard',
    trackMemory: true,
    logSlowRenders: true 
  })
  const { apiMetrics } = useApiPerformance()
  const pageMetrics = usePagePerformance('Performance Dashboard')
  const [memoryInfo, setMemoryInfo] = useState<any>(null)

  useEffect(() => {
    const updateMemoryInfo = () => {
      setMemoryInfo(performanceUtils.getMemoryUsage())
    }
    
    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000) // Update every 5 seconds
    
    return () => clearInterval(interval)
  }, [])

  const optimizations = [
    {
      title: 'Database Indexes',
      description: 'Added composite indexes for faster queries',
      status: 'implemented',
      impact: 'High',
      details: [
        'Message queries: recipientEmail + isDeleted + sentAt',
        'Registration queries: gender + createdAt',
        'Room queries: gender + isActive',
        'Notification queries: recipientId + isRead + createdAt'
      ]
    },
    {
      title: 'React Query Caching',
      description: 'Intelligent caching with stale-while-revalidate',
      status: 'implemented',
      impact: 'High',
      details: [
        'Messages cached for 2-5 minutes',
        'Accommodations cached for 5 minutes',
        'Registrations cached for 3 minutes',
        'Automatic cache invalidation on mutations'
      ]
    },
    {
      title: 'API Pagination',
      description: 'Paginated responses to reduce payload size',
      status: 'implemented',
      impact: 'Medium',
      details: [
        'Default 20 items per page',
        'Selective field loading',
        'Optimized count queries',
        'Pagination metadata included'
      ]
    },
    {
      title: 'Virtual Scrolling',
      description: 'Render only visible items in large lists',
      status: 'implemented',
      impact: 'High',
      details: [
        'Message lists virtualized',
        'Room grids virtualized',
        'Configurable overscan',
        'Smooth scrolling performance'
      ]
    },
    {
      title: 'Dynamic Imports',
      description: 'Code splitting for better initial load',
      status: 'implemented',
      impact: 'Medium',
      details: [
        'Heavy components lazy loaded',
        'Route-based code splitting',
        'Preloading on hover/intersection',
        'Error boundaries for failed imports'
      ]
    },
    {
      title: 'Image Optimization',
      description: 'Optimized image loading and caching',
      status: 'implemented',
      impact: 'Medium',
      details: [
        'Next.js Image component',
        'Lazy loading with intersection observer',
        'Progressive loading',
        'WebP format support'
      ]
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'implemented': return 'text-green-600 bg-green-100'
      case 'in-progress': return 'text-yellow-600 bg-yellow-100'
      case 'planned': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'High': return 'text-red-600'
      case 'Medium': return 'text-yellow-600'
      case 'Low': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="space-y-6">
      {/* Performance Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-apercu-medium text-gray-600">Last Render</p>
              <p className="text-lg font-apercu-bold text-gray-900">
                {metrics.lastRenderTime.toFixed(2)}ms
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-apercu-medium text-gray-600">Rerenders</p>
              <p className="text-lg font-apercu-bold text-gray-900">
                {metrics.rerenders}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Database className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-apercu-medium text-gray-600">Memory Usage</p>
              <p className="text-lg font-apercu-bold text-gray-900">
                {memoryInfo ? `${memoryInfo.used}MB` : 'N/A'}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <TrendingUp className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-apercu-medium text-gray-600">LCP</p>
              <p className="text-lg font-apercu-bold text-gray-900">
                {pageMetrics.largestContentfulPaint > 0 
                  ? `${(pageMetrics.largestContentfulPaint / 1000).toFixed(2)}s`
                  : 'Measuring...'
                }
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* API Performance */}
      {Object.keys(apiMetrics).length > 0 && (
        <Card className="p-6">
          <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">API Performance</h3>
          <div className="space-y-3">
            {Object.entries(apiMetrics).map(([apiName, metrics]) => (
              <div key={apiName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-apercu-medium text-gray-900">{apiName}</p>
                  <p className="text-sm text-gray-600">
                    {metrics.calls} calls • {metrics.errors} errors
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-apercu-bold text-gray-900">
                    {metrics.averageTime.toFixed(2)}ms avg
                  </p>
                  <p className="text-sm text-gray-600">
                    Last: {metrics.lastCallTime.toFixed(2)}ms
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Optimization Status */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="font-apercu-bold text-lg text-gray-900">Performance Optimizations</h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => performanceUtils.logAllEntries()}
            >
              <Info className="h-4 w-4 mr-2" />
              Log Metrics
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => performanceUtils.monitorFPS()}
            >
              <Activity className="h-4 w-4 mr-2" />
              Monitor FPS
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {optimizations.map((optimization, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-apercu-bold text-gray-900">{optimization.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{optimization.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-apercu-medium ${getStatusColor(optimization.status)}`}>
                    {optimization.status}
                  </span>
                  <span className={`text-xs font-apercu-bold ${getImpactColor(optimization.impact)}`}>
                    {optimization.impact}
                  </span>
                </div>
              </div>
              
              <div className="space-y-1">
                {optimization.details.map((detail, detailIndex) => (
                  <div key={detailIndex} className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                    <span className="text-xs text-gray-600">{detail}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Performance Tips */}
      <Card className="p-6">
        <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">Performance Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Zap className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Use React.memo</p>
                <p className="text-sm text-gray-600">Prevent unnecessary re-renders of components</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Database className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Optimize Database Queries</p>
                <p className="text-sm text-gray-600">Use indexes and limit result sets</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <ImageIcon className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Optimize Images</p>
                <p className="text-sm text-gray-600">Use WebP format and lazy loading</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Activity className="h-5 w-5 text-purple-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Monitor Bundle Size</p>
                <p className="text-sm text-gray-600">Use dynamic imports for large components</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Clock className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Cache API Responses</p>
                <p className="text-sm text-gray-600">Implement proper caching strategies</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
              <div>
                <p className="font-apercu-medium text-gray-900">Avoid Memory Leaks</p>
                <p className="text-sm text-gray-600">Clean up event listeners and timers</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
