'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar } from '@/components/ui/avatar'
import { ArrowRight, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import Link from 'next/link'

interface Registration {
  id: string
  fullName: string
  emailAddress: string
  dateOfBirth: string
  createdAt: string
  parentalPermissionGranted: boolean
}

interface RecentRegistrationsProps {
  registrations: Registration[]
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

function calculateAge(dateOfBirth: string): number {
  const birth = new Date(dateOfBirth)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

export function RecentRegistrations({ registrations }: RecentRegistrationsProps) {
  const recentRegistrations = registrations.slice(0, 5)

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="font-apercu-bold text-lg text-gray-900">Recent Registrations</h3>
          <p className="font-apercu-regular text-sm text-gray-600">Latest participant sign-ups</p>
        </div>
        <Link href="/admin/registrations">
          <Button variant="outline" size="sm" className="font-apercu-medium">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>

      <div className="space-y-4">
        {recentRegistrations.length === 0 ? (
          <div className="text-center py-8">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Clock className="h-6 w-6 text-gray-400" />
            </div>
            <p className="font-apercu-medium text-gray-500">No recent registrations</p>
            <p className="font-apercu-regular text-sm text-gray-400">New registrations will appear here</p>
          </div>
        ) : (
          recentRegistrations.map((registration) => (
            <div key={registration.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <Avatar className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-white font-apercu-bold text-sm">
                  {getInitials(registration.fullName)}
                </span>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <p className="font-apercu-medium text-sm text-gray-900 truncate">
                    {registration.fullName}
                  </p>
                  <Badge
                    variant={registration.parentalPermissionGranted ? "success" : "warning"}
                    className="h-5 px-2 text-xs font-apercu-medium"
                  >
                    {registration.parentalPermissionGranted ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Approved
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Pending
                      </>
                    )}
                  </Badge>
                </div>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span className="font-apercu-regular">{registration.emailAddress}</span>
                  <span className="font-apercu-regular">Age: {calculateAge(registration.dateOfBirth)}</span>
                  <span className="font-apercu-regular">{formatDate(registration.createdAt)}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </Card>
  )
}
