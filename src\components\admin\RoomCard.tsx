'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { parseApiError } from '@/lib/error-messages'
import {
  Home,
  Users,
  Edit,
  Trash2,
  UserMinus,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react'

interface Room {
  id: string
  name: string
  gender: string
  capacity: number
  isActive: boolean
  description?: string
  occupancy: number
  availableSpaces: number
  occupancyRate: number
  allocations: Array<{
    id: string
    registration: {
      id: string
      fullName: string
      gender: string
      dateOfBirth: string
      phoneNumber: string
      emailAddress: string
    }
  }>
}

interface RoomCardProps {
  room: Room
  onEdit: (room: Room) => void
  onRefresh: () => void
  onPersonPreview?: (registrationId: string) => void
}

export function RoomCard({ room, onEdit, onRefresh, onPersonPreview }: RoomCardProps) {
  const [showAllocations, setShowAllocations] = useState(false)
  const [removing, setRemoving] = useState<string | null>(null)
  const [deleting, setDeleting] = useState(false)

  const { addToast } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({ title, type })
  }

  const handleRemoveAllocation = async (registrationId: string) => {
    try {
      setRemoving(registrationId)

      const response = await fetch(`/api/admin/accommodations?registrationId=${registrationId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to remove allocation')
      }

      showToast('Allocation removed successfully', 'success')
      onRefresh()
    } catch (error) {
      console.error('Error removing allocation:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setRemoving(null)
    }
  }

  const handleDeleteRoom = async () => {
    if (room.occupancy > 0) {
      showToast('Cannot delete room with existing allocations', 'error')
      return
    }

    if (!confirm(`Are you sure you want to delete "${room.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeleting(true)

      const response = await fetch(`/api/admin/rooms/${room.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete room')
      }

      showToast('Room deleted successfully', 'success')
      onRefresh()
    } catch (error) {
      console.error('Error deleting room:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setDeleting(false)
    }
  }

  const getOccupancyColor = (rate: number) => {
    if (rate >= 90) return 'text-red-600 bg-red-50'
    if (rate >= 70) return 'text-amber-600 bg-amber-50'
    return 'text-green-600 bg-green-50'
  }

  const getGenderColor = (gender: string) => {
    return gender === 'Male' ? 'text-blue-600 bg-blue-50' : 'text-pink-600 bg-pink-50'
  }

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
      {/* Room Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Home className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="font-apercu-bold text-lg text-gray-900">{room.name}</h3>
            <div className="flex items-center space-x-2 mt-1">
              <Badge className={`${getGenderColor(room.gender)} border-0 font-apercu-medium text-xs`}>
                {room.gender}
              </Badge>
              <Badge className={`${getOccupancyColor(room.occupancyRate)} border-0 font-apercu-medium text-xs`}>
                {room.occupancyRate}% occupied
              </Badge>
              {!room.isActive && (
                <Badge variant="secondary" className="font-apercu-medium text-xs">
                  Inactive
                </Badge>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(room)}
            className="font-apercu-medium"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteRoom}
            disabled={room.occupancy > 0 || deleting}
            className="font-apercu-medium text-red-600 hover:text-red-700"
          >
            {deleting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Room Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <p className="font-apercu-bold text-xl text-gray-900">{room.occupancy}</p>
          <p className="font-apercu-medium text-xs text-gray-600">Occupied</p>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <p className="font-apercu-bold text-xl text-gray-900">{room.availableSpaces}</p>
          <p className="font-apercu-medium text-xs text-gray-600">Available</p>
        </div>
      </div>

      {/* Capacity Info */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-gray-500" />
          <span className="font-apercu-medium text-sm text-gray-700">
            {room.occupancy}/{room.capacity} persons
          </span>
        </div>

        {room.occupancy > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAllocations(!showAllocations)}
            className="font-apercu-medium text-xs"
          >
            {showAllocations ? (
              <>
                <EyeOff className="h-3 w-3 mr-1" />
                Hide
              </>
            ) : (
              <>
                <Eye className="h-3 w-3 mr-1" />
                View
              </>
            )}
          </Button>
        )}
      </div>

      {/* Description */}
      {room.description && (
        <p className="font-apercu-regular text-sm text-gray-600 mb-4">
          {room.description}
        </p>
      )}

      {/* Allocations List */}
      {showAllocations && room.allocations.length > 0 && (
        <div className="border-t border-gray-200 pt-4">
          <h4 className="font-apercu-bold text-sm text-gray-900 mb-3">Current Occupants</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {room.allocations.map((allocation) => {
              const age = new Date().getFullYear() - new Date(allocation.registration.dateOfBirth).getFullYear()
              return (
                <div key={allocation.id} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-apercu-medium text-sm text-gray-900">
                      {allocation.registration.fullName}
                    </p>
                    <p className="font-apercu-regular text-xs text-gray-500">
                      {age} years old • {allocation.registration.gender}
                    </p>
                    <p className="font-apercu-regular text-xs text-gray-400">
                      📞 {allocation.registration.phoneNumber}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {onPersonPreview && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPersonPreview(allocation.registration.id)}
                        className="font-apercu-medium text-indigo-600 hover:text-indigo-700"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAllocation(allocation.registration.id)}
                      disabled={removing === allocation.registration.id}
                      className="font-apercu-medium text-red-600 hover:text-red-700"
                    >
                      {removing === allocation.registration.id ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <UserMinus className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {/* Progress Bar */}
      <div className="mt-4">
        <div className="flex items-center justify-between mb-2">
          <span className="font-apercu-medium text-xs text-gray-600">Occupancy</span>
          <span className="font-apercu-medium text-xs text-gray-600">{room.occupancyRate}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              room.occupancyRate >= 90 ? 'bg-red-500' :
              room.occupancyRate >= 70 ? 'bg-amber-500' : 'bg-green-500'
            }`}
            style={{ width: `${room.occupancyRate}%` }}
          />
        </div>
      </div>
    </Card>
  )
}
