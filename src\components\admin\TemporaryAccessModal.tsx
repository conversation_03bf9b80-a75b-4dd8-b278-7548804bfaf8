'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'
import { parseApiError } from '@/lib/error-messages'
import { ConfirmDialog } from '@/components/ui/message-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Shield,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  X,
  Loader2
} from 'lucide-react'

interface TemporaryAccess {
  id: string
  temporaryRole: string
  grantedBy: string
  reason: string
  expiresAt: string
  timeRemaining: number
}

interface TemporaryAccessModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string | null
  userName: string
  currentRole: string
  onAccessGranted?: () => void
}

const AVAILABLE_ROLES = [
  { value: 'Admin', label: 'Admin', description: 'Full administrative access' },
  { value: 'Manager', label: 'Manager', description: 'Management level access' },
  { value: 'Staff', label: 'Staff', description: 'Staff level access' }
]

const DURATION_OPTIONS = [
  { value: 1, label: '1 hour' },
  { value: 2, label: '2 hours' },
  { value: 4, label: '4 hours' },
  { value: 8, label: '8 hours' },
  { value: 12, label: '12 hours' },
  { value: 24, label: '24 hours' },
  { value: 48, label: '48 hours' },
  { value: 72, label: '72 hours (max)' }
]

export function TemporaryAccessModal({
  isOpen,
  onClose,
  userId,
  userName,
  currentRole,
  onAccessGranted
}: TemporaryAccessModalProps) {
  const [temporaryAccess, setTemporaryAccess] = useState<TemporaryAccess | null>(null)
  const [loading, setLoading] = useState(false)
  const [granting, setGranting] = useState(false)
  const [revoking, setRevoking] = useState(false)
  const [showRevokeConfirm, setShowRevokeConfirm] = useState(false)

  // Form state
  const [selectedRole, setSelectedRole] = useState('')
  const [duration, setDuration] = useState<number>(4)
  const [reason, setReason] = useState('')

  const { addToast } = useToast()

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({ title, type })
  }

  // Fetch current temporary access status
  const fetchTemporaryAccess = async () => {
    if (!userId) return

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/users/${userId}/temporary-access`)
      
      if (response.ok) {
        const data = await response.json()
        setTemporaryAccess(data.hasTemporaryAccess ? data.temporaryAccess : null)
      }
    } catch (error) {
      console.error('Error fetching temporary access:', error)
    } finally {
      setLoading(false)
    }
  }

  // Grant temporary access
  const handleGrantAccess = async () => {
    if (!userId || !selectedRole || !reason.trim()) {
      showToast('Please fill in all required fields', 'error')
      return
    }

    try {
      setGranting(true)

      const response = await fetch(`/api/admin/users/${userId}/temporary-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          temporaryRole: selectedRole,
          duration,
          reason: reason.trim()
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to grant temporary access')
      }

      showToast('Temporary access granted successfully', 'success')
      
      // Reset form
      setSelectedRole('')
      setDuration(4)
      setReason('')
      
      // Refresh data
      await fetchTemporaryAccess()
      
      if (onAccessGranted) {
        onAccessGranted()
      }

    } catch (error) {
      console.error('Error granting temporary access:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setGranting(false)
    }
  }

  // Revoke temporary access
  const handleRevokeAccess = async () => {
    if (!userId) return

    try {
      setRevoking(true)
      setShowRevokeConfirm(false)

      const response = await fetch(`/api/admin/users/${userId}/temporary-access`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to revoke temporary access')
      }

      showToast('Temporary access revoked successfully', 'success')
      
      // Refresh data
      await fetchTemporaryAccess()
      
      if (onAccessGranted) {
        onAccessGranted()
      }

    } catch (error) {
      console.error('Error revoking temporary access:', error)
      const errorMessage = parseApiError(error)
      showToast(errorMessage.description, 'error')
    } finally {
      setRevoking(false)
    }
  }

  // Format time remaining
  const formatTimeRemaining = (hours: number): string => {
    if (hours < 1) {
      return 'Less than 1 hour'
    } else if (hours === 1) {
      return '1 hour'
    } else if (hours < 24) {
      return `${hours} hours`
    } else {
      const days = Math.floor(hours / 24)
      const remainingHours = hours % 24
      return `${days} day${days > 1 ? 's' : ''}${remainingHours > 0 ? ` ${remainingHours} hour${remainingHours > 1 ? 's' : ''}` : ''}`
    }
  }

  useEffect(() => {
    if (isOpen && userId) {
      fetchTemporaryAccess()
    }
  }, [isOpen, userId])

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="font-apercu-bold text-lg">
                  Temporary Access Management
                </DialogTitle>
                <DialogDescription className="font-apercu-regular text-sm">
                  Manage temporary role elevation for {userName}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
            </div>
          ) : (
            <div className="space-y-6">
              {/* Current Status */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-apercu-bold text-sm text-gray-900">Current Status</h4>
                  <Badge variant={temporaryAccess ? 'warning' : 'secondary'}>
                    {temporaryAccess ? 'Elevated Access' : 'Normal Access'}
                  </Badge>
                </div>
                <p className="font-apercu-regular text-sm text-gray-600">
                  Current Role: <span className="font-apercu-medium">{currentRole}</span>
                </p>
                
                {temporaryAccess && (
                  <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="h-4 w-4 text-amber-600" />
                      <span className="font-apercu-bold text-sm text-amber-900">
                        Temporary {temporaryAccess.temporaryRole} Access
                      </span>
                    </div>
                    <p className="font-apercu-regular text-xs text-amber-700 mb-1">
                      Granted by: {temporaryAccess.grantedBy}
                    </p>
                    <p className="font-apercu-regular text-xs text-amber-700 mb-1">
                      Reason: {temporaryAccess.reason}
                    </p>
                    <p className="font-apercu-regular text-xs text-amber-700">
                      Time remaining: {formatTimeRemaining(temporaryAccess.timeRemaining)}
                    </p>
                  </div>
                )}
              </div>

              {/* Grant Access Form */}
              {!temporaryAccess && (
                <div className="space-y-4">
                  <h4 className="font-apercu-bold text-sm text-gray-900">Grant Temporary Access</h4>
                  
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="role" className="font-apercu-medium text-sm">
                        Temporary Role *
                      </Label>
                      <Select value={selectedRole} onValueChange={setSelectedRole}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {AVAILABLE_ROLES.map((role) => (
                            <SelectItem key={role.value} value={role.value}>
                              <div>
                                <div className="font-apercu-medium">{role.label}</div>
                                <div className="font-apercu-regular text-xs text-gray-500">
                                  {role.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="duration" className="font-apercu-medium text-sm">
                        Duration *
                      </Label>
                      <Select value={duration.toString()} onValueChange={(value) => setDuration(parseInt(value))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {DURATION_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="reason" className="font-apercu-medium text-sm">
                        Reason *
                      </Label>
                      <Textarea
                        id="reason"
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Explain why temporary access is needed..."
                        className="font-apercu-regular"
                        rows={3}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="flex items-center justify-between">
            <Button
              onClick={onClose}
              variant="outline"
              className="font-apercu-medium"
            >
              Close
            </Button>
            
            <div className="flex space-x-2">
              {temporaryAccess ? (
                <Button
                  onClick={() => setShowRevokeConfirm(true)}
                  variant="destructive"
                  disabled={revoking}
                  className="font-apercu-medium"
                >
                  {revoking ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Revoking...
                    </>
                  ) : (
                    <>
                      <X className="h-4 w-4 mr-2" />
                      Revoke Access
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleGrantAccess}
                  disabled={granting || !selectedRole || !reason.trim()}
                  className="font-apercu-medium bg-amber-600 hover:bg-amber-700"
                >
                  {granting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Granting...
                    </>
                  ) : (
                    <>
                      <Shield className="h-4 w-4 mr-2" />
                      Grant Access
                    </>
                  )}
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Revoke Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showRevokeConfirm}
        onClose={() => setShowRevokeConfirm(false)}
        onConfirm={handleRevokeAccess}
        title="Revoke Temporary Access"
        message={
          <div>
            <p className="mb-2">
              Are you sure you want to revoke temporary <span className="font-apercu-bold text-red-600">{temporaryAccess?.temporaryRole}</span> access for <span className="font-apercu-bold">{userName}</span>?
            </p>
            <p className="text-sm text-gray-600">
              Their role will be restored to <span className="font-apercu-bold">{currentRole}</span> immediately.
            </p>
          </div>
        }
        confirmText={revoking ? 'Revoking...' : 'Revoke Access'}
        cancelText="Cancel"
        loading={revoking}
      />
    </>
  )
}
