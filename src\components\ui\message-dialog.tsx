'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'

interface MessageDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm?: () => void
  type?: 'info' | 'success' | 'warning' | 'error' | 'confirm'
  title: string
  message: string | ReactNode
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  loading?: boolean
  className?: string
}

export function MessageDialog({
  isOpen,
  onClose,
  onConfirm,
  type = 'info',
  title,
  message,
  confirmText = 'OK',
  cancelText = 'Cancel',
  showCancel = false,
  loading = false,
  className
}: MessageDialogProps) {
  if (!isOpen) return null

  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          iconColor: 'text-green-600',
          iconBg: 'bg-green-100',
          borderColor: 'border-green-200',
          titleColor: 'text-green-900',
          confirmVariant: 'default' as const
        }
      case 'warning':
        return {
          icon: AlertTriangle,
          iconColor: 'text-amber-600',
          iconBg: 'bg-amber-100',
          borderColor: 'border-amber-200',
          titleColor: 'text-amber-900',
          confirmVariant: 'default' as const
        }
      case 'error':
        return {
          icon: AlertCircle,
          iconColor: 'text-red-600',
          iconBg: 'bg-red-100',
          borderColor: 'border-red-200',
          titleColor: 'text-red-900',
          confirmVariant: 'destructive' as const
        }
      case 'confirm':
        return {
          icon: AlertTriangle,
          iconColor: 'text-amber-600',
          iconBg: 'bg-amber-100',
          borderColor: 'border-amber-200',
          titleColor: 'text-amber-900',
          confirmVariant: 'destructive' as const
        }
      default: // info
        return {
          icon: Info,
          iconColor: 'text-blue-600',
          iconBg: 'bg-blue-100',
          borderColor: 'border-blue-200',
          titleColor: 'text-blue-900',
          confirmVariant: 'default' as const
        }
    }
  }

  const config = getTypeConfig()
  const Icon = config.icon

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
      <div 
        className={cn(
          'bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 border-2 transform transition-all duration-200 scale-100',
          config.borderColor,
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-start justify-between p-6 pb-4">
          <div className="flex items-start space-x-4">
            <div className={cn(
              'h-12 w-12 rounded-full flex items-center justify-center flex-shrink-0',
              config.iconBg
            )}>
              <Icon className={cn('h-6 w-6', config.iconColor)} />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className={cn(
                'font-apercu-bold text-lg leading-6',
                config.titleColor
              )}>
                {title}
              </h3>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-lg hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          <div className="text-gray-700 font-apercu-regular leading-relaxed">
            {typeof message === 'string' ? (
              <p>{message}</p>
            ) : (
              message
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 px-6 py-4 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {showCancel && (
            <Button
              onClick={onClose}
              variant="outline"
              disabled={loading}
              className="font-apercu-medium"
            >
              {cancelText}
            </Button>
          )}
          <Button
            onClick={onConfirm || onClose}
            variant={config.confirmVariant}
            disabled={loading}
            className="font-apercu-medium min-w-[80px]"
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                <span>Loading...</span>
              </div>
            ) : (
              confirmText
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}

// Convenience components for specific types
export function SuccessDialog(props: Omit<MessageDialogProps, 'type'>) {
  return <MessageDialog {...props} type="success" />
}

export function ErrorDialog(props: Omit<MessageDialogProps, 'type'>) {
  return <MessageDialog {...props} type="error" />
}

export function WarningDialog(props: Omit<MessageDialogProps, 'type'>) {
  return <MessageDialog {...props} type="warning" />
}

export function ConfirmDialog(props: Omit<MessageDialogProps, 'type' | 'showCancel'>) {
  return <MessageDialog {...props} type="confirm" showCancel={true} />
}

export function InfoDialog(props: Omit<MessageDialogProps, 'type'>) {
  return <MessageDialog {...props} type="info" />
}
