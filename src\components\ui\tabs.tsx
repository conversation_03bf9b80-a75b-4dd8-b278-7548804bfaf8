'use client'

import { useState, ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface TabItem {
  id: string
  label: string
  content: ReactNode
  badge?: string | number
  icon?: ReactNode
}

interface TabsProps {
  items: TabItem[]
  defaultTab?: string
  className?: string
  tabClassName?: string
  contentClassName?: string
  variant?: 'default' | 'pills' | 'underline'
}

export function Tabs({ 
  items, 
  defaultTab, 
  className = '', 
  tabClassName = '',
  contentClassName = '',
  variant = 'default'
}: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || items[0]?.id)

  const getTabStyles = () => {
    switch (variant) {
      case 'pills':
        return {
          container: 'bg-gray-100 p-1 rounded-lg',
          tab: 'px-3 py-2 rounded-md font-apercu-medium text-sm transition-all duration-200',
          active: 'bg-white text-gray-900 shadow-sm',
          inactive: 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        }
      case 'underline':
        return {
          container: 'border-b border-gray-200',
          tab: 'px-4 py-3 font-apercu-medium text-sm border-b-2 transition-all duration-200',
          active: 'border-indigo-500 text-indigo-600',
          inactive: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        }
      default:
        return {
          container: 'bg-gray-50 rounded-lg p-1',
          tab: 'px-4 py-2 rounded-md font-apercu-medium text-sm transition-all duration-200',
          active: 'bg-white text-gray-900 shadow-sm',
          inactive: 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
        }
    }
  }

  const styles = getTabStyles()
  const activeItem = items.find(item => item.id === activeTab)

  return (
    <div className={cn('w-full', className)}>
      {/* Tab Navigation */}
      <div className={cn('flex space-x-1', styles.container, tabClassName)}>
        {items.map((item) => (
          <button
            key={item.id}
            onClick={() => setActiveTab(item.id)}
            className={cn(
              styles.tab,
              activeTab === item.id ? styles.active : styles.inactive
            )}
          >
            <div className="flex items-center space-x-2">
              {item.icon && (
                <span className="flex-shrink-0">
                  {item.icon}
                </span>
              )}
              <span>{item.label}</span>
              {item.badge && (
                <span className={cn(
                  'px-2 py-0.5 text-xs rounded-full font-apercu-medium',
                  activeTab === item.id 
                    ? 'bg-indigo-100 text-indigo-700' 
                    : 'bg-gray-200 text-gray-600'
                )}>
                  {item.badge}
                </span>
              )}
            </div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className={cn('mt-4', contentClassName)}>
        {activeItem?.content}
      </div>
    </div>
  )
}

// Gender-specific tab component for male/female containers
interface GenderTabsProps {
  maleContent: ReactNode
  femaleContent: ReactNode
  maleCount?: number
  femaleCount?: number
  className?: string
}

export function GenderTabs({ 
  maleContent, 
  femaleContent, 
  maleCount, 
  femaleCount, 
  className = '' 
}: GenderTabsProps) {
  const tabItems: TabItem[] = [
    {
      id: 'male',
      label: 'Male',
      content: maleContent,
      badge: maleCount,
      icon: (
        <div className="h-4 w-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
          <div className="h-2 w-2 bg-white rounded-full"></div>
        </div>
      )
    },
    {
      id: 'female',
      label: 'Female',
      content: femaleContent,
      badge: femaleCount,
      icon: (
        <div className="h-4 w-4 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
          <div className="h-2 w-2 bg-white rounded-full"></div>
        </div>
      )
    }
  ]

  return (
    <Tabs 
      items={tabItems} 
      variant="pills" 
      className={className}
      tabClassName="justify-center sm:justify-start"
    />
  )
}
