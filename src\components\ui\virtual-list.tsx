'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import { cn } from '@/lib/utils'

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
  onScroll?: (scrollTop: number) => void
  loading?: boolean
  loadingComponent?: React.ReactNode
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className,
  overscan = 5,
  onScroll,
  loading = false,
  loadingComponent
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const { visibleItems, totalHeight, offsetY } = useMemo(() => {
    const itemCount = items.length
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      itemCount - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )

    const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index
    }))

    return {
      visibleItems,
      totalHeight: itemCount * itemHeight,
      offsetY: startIndex * itemHeight
    }
  }, [items, itemHeight, scrollTop, containerHeight, overscan])

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop
    setScrollTop(scrollTop)
    onScroll?.(scrollTop)
  }

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center', className)} style={{ height: containerHeight }}>
        {loadingComponent || (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <span className="font-apercu-medium text-gray-600">Loading...</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      ref={scrollElementRef}
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map(({ item, index }) => (
            <div key={index} style={{ height: itemHeight }}>
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Optimized message list component
interface VirtualMessageListProps {
  messages: any[]
  onMessageClick: (message: any) => void
  selectedMessageId?: string
  className?: string
  loading?: boolean
}

export function VirtualMessageList({
  messages,
  onMessageClick,
  selectedMessageId,
  className,
  loading = false
}: VirtualMessageListProps) {
  const renderMessage = (message: any, index: number) => (
    <div
      onClick={() => onMessageClick(message)}
      className={cn(
        'p-3 rounded-lg cursor-pointer transition-colors border-b border-gray-100',
        selectedMessageId === message.id
          ? 'bg-indigo-50 border border-indigo-200'
          : message.readAt
          ? 'bg-gray-50 hover:bg-gray-100'
          : 'bg-blue-50 hover:bg-blue-100 border border-blue-200'
      )}
    >
      <div className="flex items-start justify-between mb-1">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="font-apercu-bold text-white text-xs">
              {message.senderName.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="font-apercu-bold text-sm text-gray-900 truncate">
                {message.senderName}
              </span>
              <span className="font-apercu-regular text-xs text-gray-500">
                {new Date(message.sentAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
        {!message.readAt && (
          <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0"></div>
        )}
      </div>
      <div className="flex items-center justify-between mb-1">
        <p className="font-apercu-medium text-sm text-gray-900 truncate flex-1">
          {message.subject}
        </p>
        {message.replies && message.replies.length > 0 && (
          <span className="ml-2 px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded-full">
            {message.replies.length} replies
          </span>
        )}
      </div>
      <p className="font-apercu-regular text-xs text-gray-600 line-clamp-2">
        {message.content}
      </p>
    </div>
  )

  return (
    <VirtualList
      items={messages}
      itemHeight={100} // Approximate height of each message item
      containerHeight={400} // Height of the message list container
      renderItem={renderMessage}
      className={className}
      loading={loading}
      overscan={3}
    />
  )
}

// Optimized room grid component
interface VirtualRoomGridProps {
  rooms: any[]
  onRoomEdit: (room: any) => void
  onRefresh: () => void
  className?: string
  loading?: boolean
  itemsPerRow?: number
}

export function VirtualRoomGrid({
  rooms,
  onRoomEdit,
  onRefresh,
  className,
  loading = false,
  itemsPerRow = 4
}: VirtualRoomGridProps) {
  // Group rooms into rows for virtual scrolling
  const roomRows = useMemo(() => {
    const rows = []
    for (let i = 0; i < rooms.length; i += itemsPerRow) {
      rows.push(rooms.slice(i, i + itemsPerRow))
    }
    return rows
  }, [rooms, itemsPerRow])

  const renderRow = (row: any[], index: number) => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6 px-4">
      {row.map((room) => (
        <div key={room.id} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-apercu-bold text-lg text-gray-900">{room.name}</h3>
            <span className={cn(
              'px-2 py-1 rounded-full text-xs font-apercu-medium',
              room.gender === 'Male' 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-pink-100 text-pink-700'
            )}>
              {room.gender}
            </span>
          </div>
          
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="font-apercu-medium text-gray-600">Capacity:</span>
              <span className="font-apercu-bold text-gray-900">{room.capacity}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="font-apercu-medium text-gray-600">Occupied:</span>
              <span className="font-apercu-bold text-gray-900">{room.occupancy}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="font-apercu-medium text-gray-600">Available:</span>
              <span className="font-apercu-bold text-gray-900">{room.availableSpaces}</span>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => onRoomEdit(room)}
              className="flex-1 px-3 py-2 bg-indigo-600 text-white rounded-lg font-apercu-medium text-sm hover:bg-indigo-700 transition-colors"
            >
              Edit
            </button>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <VirtualList
      items={roomRows}
      itemHeight={280} // Approximate height of each room row
      containerHeight={600} // Height of the room grid container
      renderItem={renderRow}
      className={className}
      loading={loading}
      overscan={2}
    />
  )
}
