'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useUser } from '@/contexts/UserContext'

interface MessageStats {
  total: number
  unread: number
  thisWeek: number
}

interface MessageContextType {
  stats: MessageStats
  refreshStats: () => Promise<void>
  markAsRead: (messageId: string) => void
  deleteMessage: (messageId: string) => void
}

const MessageContext = createContext<MessageContextType | undefined>(undefined)

interface MessageProviderProps {
  children: ReactNode
}

export function MessageProvider({ children }: MessageProviderProps) {
  const [stats, setStats] = useState<MessageStats>({
    total: 0,
    unread: 0,
    thisWeek: 0
  })
  const { currentUser } = useUser()

  const fetchStats = async () => {
    if (!currentUser) return

    try {
      const response = await fetch('/api/admin/messages/inbox')
      if (response.ok) {
        const data = await response.json()
        const messages = data.messages || []

        // Calculate stats
        const total = messages.length
        const unread = messages.filter((msg: any) => !msg.readAt).length
        const thisWeek = messages.filter((msg: any) => {
          const msgDate = new Date(msg.sentAt)
          const weekAgo = new Date()
          weekAgo.setDate(weekAgo.getDate() - 7)
          return msgDate > weekAgo
        }).length

        setStats({ total, unread, thisWeek })
      }
    } catch (error) {
      console.error('Failed to fetch message stats:', error)
      // Reset stats on error
      setStats({ total: 0, unread: 0, thisWeek: 0 })
    }
  }

  const refreshStats = async () => {
    await fetchStats()
  }

  const markAsRead = async (messageId: string) => {
    try {
      // Call API to mark message as read
      const response = await fetch(`/api/admin/messages/${messageId}/read`, {
        method: 'PUT'
      })

      if (response.ok) {
        // Only update local state if API call succeeds
        setStats(prev => ({
          ...prev,
          unread: Math.max(0, prev.unread - 1)
        }))
      } else {
        console.error('Failed to mark message as read')
      }
    } catch (error) {
      console.error('Error marking message as read:', error)
    }
  }

  const deleteMessage = async (messageId: string) => {
    try {
      // Call API to delete message
      const response = await fetch(`/api/admin/messages/${messageId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        // Update local state if API call succeeds
        setStats(prev => ({
          ...prev,
          total: Math.max(0, prev.total - 1)
        }))
        // Refresh stats to get accurate counts
        await fetchStats()
      } else {
        console.error('Failed to delete message')
      }
    } catch (error) {
      console.error('Error deleting message:', error)
    }
  }

  useEffect(() => {
    if (currentUser) {
      fetchStats()

      // Set up polling to update stats every 10 seconds for real-time messaging
      const interval = setInterval(fetchStats, 10000)

      return () => clearInterval(interval)
    } else {
      // Reset stats when user logs out
      setStats({ total: 0, unread: 0, thisWeek: 0 })
    }
  }, [currentUser])

  const value: MessageContextType = {
    stats,
    refreshStats,
    markAsRead,
    deleteMessage
  }

  return (
    <MessageContext.Provider value={value}>
      {children}
    </MessageContext.Provider>
  )
}

export function useMessages() {
  const context = useContext(MessageContext)
  if (context === undefined) {
    throw new Error('useMessages must be used within a MessageProvider')
  }
  return context
}
