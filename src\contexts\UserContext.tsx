'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface UserRole {
  id: string
  name: string
  permissions: Array<{
    id: string
    name: string
  }>
}

interface CurrentUser {
  id: string
  email: string
  name: string
  type: 'admin' | 'user'
  role: UserRole
  isActive: boolean
}

interface UserContextType {
  currentUser: CurrentUser | null
  loading: boolean
  refreshUser: () => Promise<void>
  hasPermission: (permission: string) => boolean
  isRole: (roleName: string) => boolean
}

const UserContext = createContext<UserContextType | undefined>(undefined)

interface UserProviderProps {
  children: ReactNode
}

export function UserProvider({ children }: UserProviderProps) {
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchCurrentUser = async () => {
    try {
      console.log('🔄 Fetching current user...')
      const response = await fetch('/api/auth/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      console.log('📡 Auth response status:', response.status)

      if (response.ok) {
        const userData = await response.json()
        console.log('✅ User data received:', userData)
        setCurrentUser(userData.user)
      } else {
        console.log('❌ Auth failed, status:', response.status)
        setCurrentUser(null)
      }
    } catch (error) {
      console.error('❌ Failed to fetch current user:', error)
      setCurrentUser(null)
    } finally {
      console.log('🏁 Setting loading to false')
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    await fetchCurrentUser()
  }

  const hasPermission = (permission: string): boolean => {
    if (!currentUser?.role?.permissions) return false
    
    // Super Admin and Admin roles have all permissions
    if (currentUser.role.name === 'Super Admin' || currentUser.role.name === 'Admin') {
      return true
    }
    
    return currentUser.role.permissions.some(p => 
      p.name === permission || p.name === 'system:admin'
    )
  }

  const isRole = (roleName: string): boolean => {
    return currentUser?.role?.name === roleName
  }

  useEffect(() => {
    fetchCurrentUser()
  }, [])

  const value: UserContextType = {
    currentUser,
    loading,
    refreshUser,
    hasPermission,
    isRole
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
