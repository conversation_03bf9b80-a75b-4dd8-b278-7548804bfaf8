'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface PerformanceMetrics {
  renderTime: number
  memoryUsage?: number
  componentMounts: number
  rerenders: number
  lastRenderTime: number
}

interface UsePerformanceOptions {
  trackMemory?: boolean
  logSlowRenders?: boolean
  slowRenderThreshold?: number
  componentName?: string
}

export function usePerformance(options: UsePerformanceOptions = {}) {
  const {
    trackMemory = false,
    logSlowRenders = true,
    slowRenderThreshold = 16, // 16ms = 60fps
    componentName = 'Unknown Component'
  } = options

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMounts: 0,
    rerenders: 0,
    lastRenderTime: 0
  })

  const renderStartTime = useRef<number>(0)
  const mountTime = useRef<number>(0)
  const renderCount = useRef<number>(0)

  // Start performance measurement
  const startMeasurement = useCallback(() => {
    renderStartTime.current = performance.now()
  }, [])

  // End performance measurement
  const endMeasurement = useCallback(() => {
    const endTime = performance.now()
    const renderTime = endTime - renderStartTime.current
    renderCount.current += 1

    setMetrics(prev => ({
      ...prev,
      renderTime,
      lastRenderTime: renderTime,
      rerenders: renderCount.current - 1
    }))

    // Log slow renders
    if (logSlowRenders && renderTime > slowRenderThreshold) {
      console.warn(
        `🐌 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms (threshold: ${slowRenderThreshold}ms)`
      )
    }

    // Track memory usage if enabled
    if (trackMemory && 'memory' in performance) {
      const memoryInfo = (performance as any).memory
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memoryInfo.usedJSHeapSize / 1024 / 1024 // Convert to MB
      }))
    }
  }, [logSlowRenders, slowRenderThreshold, componentName, trackMemory])

  // Track component mount
  useEffect(() => {
    mountTime.current = performance.now()
    setMetrics(prev => ({
      ...prev,
      componentMounts: prev.componentMounts + 1
    }))

    return () => {
      const unmountTime = performance.now()
      const totalMountTime = unmountTime - mountTime.current
      
      if (totalMountTime > 1000) { // Log if component was mounted for less than 1 second
        console.warn(
          `⚡ Quick unmount detected in ${componentName}: mounted for only ${totalMountTime.toFixed(2)}ms`
        )
      }
    }
  }, [componentName])

  // Auto-measure renders
  useEffect(() => {
    startMeasurement()
    endMeasurement()
  })

  return {
    metrics,
    startMeasurement,
    endMeasurement
  }
}

// Hook for measuring API call performance
export function useApiPerformance() {
  const [apiMetrics, setApiMetrics] = useState<Record<string, {
    calls: number
    totalTime: number
    averageTime: number
    lastCallTime: number
    errors: number
  }>>({})

  const measureApiCall = useCallback(async <T>(
    apiName: string,
    apiCall: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now()
    
    try {
      const result = await apiCall()
      const endTime = performance.now()
      const callTime = endTime - startTime

      setApiMetrics(prev => {
        const existing = prev[apiName] || { calls: 0, totalTime: 0, averageTime: 0, lastCallTime: 0, errors: 0 }
        const newCalls = existing.calls + 1
        const newTotalTime = existing.totalTime + callTime
        
        return {
          ...prev,
          [apiName]: {
            calls: newCalls,
            totalTime: newTotalTime,
            averageTime: newTotalTime / newCalls,
            lastCallTime: callTime,
            errors: existing.errors
          }
        }
      })

      // Log slow API calls
      if (callTime > 1000) { // 1 second threshold
        console.warn(`🐌 Slow API call: ${apiName} took ${callTime.toFixed(2)}ms`)
      }

      return result
    } catch (error) {
      const endTime = performance.now()
      const callTime = endTime - startTime

      setApiMetrics(prev => {
        const existing = prev[apiName] || { calls: 0, totalTime: 0, averageTime: 0, lastCallTime: 0, errors: 0 }
        
        return {
          ...prev,
          [apiName]: {
            ...existing,
            errors: existing.errors + 1,
            lastCallTime: callTime
          }
        }
      })

      throw error
    }
  }, [])

  return {
    apiMetrics,
    measureApiCall
  }
}

// Hook for monitoring page performance
export function usePagePerformance(pageName: string) {
  const [pageMetrics, setPageMetrics] = useState({
    loadTime: 0,
    domContentLoaded: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0
  })

  useEffect(() => {
    // Measure page load time
    const loadTime = performance.now()
    
    // Get navigation timing
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      setPageMetrics(prev => ({
        ...prev,
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      }))
    }

    // Get paint timing
    const paintEntries = performance.getEntriesByType('paint')
    paintEntries.forEach((entry) => {
      if (entry.name === 'first-contentful-paint') {
        setPageMetrics(prev => ({
          ...prev,
          firstContentfulPaint: entry.startTime
        }))
      }
    })

    // Observe LCP
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        setPageMetrics(prev => ({
          ...prev,
          largestContentfulPaint: lastEntry.startTime
        }))
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (e) {
        // LCP not supported
      }

      // Observe CLS
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
          }
        }
        setPageMetrics(prev => ({
          ...prev,
          cumulativeLayoutShift: clsValue
        }))
      })
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // CLS not supported
      }

      // Observe FID
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          setPageMetrics(prev => ({
            ...prev,
            firstInputDelay: (entry as any).processingStart - entry.startTime
          }))
        }
      })
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        // FID not supported
      }

      return () => {
        lcpObserver.disconnect()
        clsObserver.disconnect()
        fidObserver.disconnect()
      }
    }
  }, [pageName])

  // Log performance metrics
  useEffect(() => {
    const timer = setTimeout(() => {
      console.log(`📊 Performance metrics for ${pageName}:`, pageMetrics)
      
      // Check for performance issues
      if (pageMetrics.largestContentfulPaint > 2500) {
        console.warn(`⚠️ LCP is slow: ${pageMetrics.largestContentfulPaint.toFixed(2)}ms (should be < 2.5s)`)
      }
      
      if (pageMetrics.cumulativeLayoutShift > 0.1) {
        console.warn(`⚠️ CLS is high: ${pageMetrics.cumulativeLayoutShift.toFixed(3)} (should be < 0.1)`)
      }
      
      if (pageMetrics.firstInputDelay > 100) {
        console.warn(`⚠️ FID is slow: ${pageMetrics.firstInputDelay.toFixed(2)}ms (should be < 100ms)`)
      }
    }, 3000) // Wait 3 seconds for metrics to stabilize

    return () => clearTimeout(timer)
  }, [pageName, pageMetrics])

  return pageMetrics
}

// Performance debugging utilities
export const performanceUtils = {
  // Log all performance entries
  logAllEntries: () => {
    console.log('🔍 All Performance Entries:')
    console.log('Navigation:', performance.getEntriesByType('navigation'))
    console.log('Paint:', performance.getEntriesByType('paint'))
    console.log('Resource:', performance.getEntriesByType('resource'))
  },

  // Clear performance entries
  clearEntries: () => {
    performance.clearResourceTimings()
    console.log('🧹 Performance entries cleared')
  },

  // Get memory usage
  getMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
    return null
  },

  // Monitor frame rate
  monitorFPS: (duration = 5000) => {
    let frames = 0
    const startTime = performance.now()
    
    function countFrames() {
      frames++
      if (performance.now() - startTime < duration) {
        requestAnimationFrame(countFrames)
      } else {
        const fps = Math.round((frames * 1000) / duration)
        console.log(`📈 Average FPS over ${duration}ms: ${fps}`)
        
        if (fps < 30) {
          console.warn('⚠️ Low FPS detected. Consider optimizing animations or reducing DOM complexity.')
        }
      }
    }
    
    requestAnimationFrame(countFrames)
  }
}
