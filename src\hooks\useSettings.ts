import { useState, useEffect } from 'react'

interface SystemSettings {
  systemName: string
  timezone: string
  dateFormat: string
  language: string
  maintenanceMode: boolean
  debugMode: boolean
}

interface UserManagementSettings {
  defaultRole: string
  selfRegistration: boolean
  passwordRequirements: string
  sessionTimeout: number
  maxUsers: number
}

interface SecuritySettings {
  twoFactor: string
  loginAttempts: number
  lockoutDuration: number
  passwordExpiry: number
  ipWhitelist: boolean
}

interface NotificationSettings {
  newRegistrationAlerts: boolean
  dailySummary: boolean
  maintenanceAlerts: boolean
  emailNotifications: string
  slackWebhook: string
}

interface AllSettings {
  system: SystemSettings
  userManagement: UserManagementSettings
  security: SecuritySettings
  notifications: NotificationSettings
}

export function useSettings() {
  const [settings, setSettings] = useState<Partial<AllSettings>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      console.log('🔄 Fetching settings...')

      const response = await fetch('/api/admin/settings', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      console.log('📡 Settings response status:', response.status)

      if (!response.ok) {
        // If unauthorized or forbidden, use default settings
        if (response.status === 401 || response.status === 403) {
          console.log('⚠️ Using default settings due to permissions')
          setSettings({
            system: {
              systemName: 'Mopgomyouth',
              timezone: 'UTC-5 (EST)',
              dateFormat: 'MM/DD/YYYY',
              language: 'en',
              maintenanceMode: false,
              debugMode: false
            }
          })
          setError(null)
          return
        }
        throw new Error(`HTTP ${response.status}: Failed to fetch settings`)
      }

      const data = await response.json()
      console.log('✅ Settings data received:', data)

      if (data.success) {
        // Transform the API response to a more usable format
        const transformedSettings: Partial<AllSettings> = {}

        Object.keys(data.settings).forEach(category => {
          const categorySettings = data.settings[category]
          const settingsObject: any = {}

          categorySettings.forEach((setting: any) => {
            settingsObject[setting.key] = setting.value
          })

          transformedSettings[category as keyof AllSettings] = settingsObject
        })

        setSettings(transformedSettings)
        setError(null)
        console.log('✅ Settings loaded successfully')
      } else {
        throw new Error('Invalid response format')
      }
    } catch (err) {
      console.error('❌ Failed to fetch settings:', err)

      // Use default settings as fallback
      console.log('🔄 Using default settings as fallback')
      setSettings({
        system: {
          systemName: 'Mopgomyouth',
          timezone: 'UTC-5 (EST)',
          dateFormat: 'MM/DD/YYYY',
          language: 'en',
          maintenanceMode: false,
          debugMode: false
        },
        userManagement: {
          defaultRole: 'Viewer',
          selfRegistration: false,
          passwordRequirements: 'Medium',
          sessionTimeout: 24,
          maxUsers: 1000
        }
      })

      setError(err instanceof Error ? err.message : 'Failed to fetch settings')
    } finally {
      setLoading(false)
      console.log('🏁 Settings fetch completed')
    }
  }

  const getSetting = (category: keyof AllSettings, key: string, defaultValue: any = null) => {
    const categorySettings = settings[category] as any
    return categorySettings?.[key] ?? defaultValue
  }

  const getSystemName = () => getSetting('system', 'systemName', 'Mopgomyouth')
  const getTimezone = () => getSetting('system', 'timezone', 'UTC-5 (EST)')
  const getDateFormat = () => getSetting('system', 'dateFormat', 'MM/DD/YYYY')
  const isMaintenanceMode = () => getSetting('system', 'maintenanceMode', false)
  const isDebugMode = () => getSetting('system', 'debugMode', false)

  const getDefaultRole = () => getSetting('userManagement', 'defaultRole', 'Viewer')
  const isSelfRegistrationEnabled = () => getSetting('userManagement', 'selfRegistration', false)
  const getPasswordRequirements = () => getSetting('userManagement', 'passwordRequirements', 'Medium')
  const getSessionTimeout = () => getSetting('userManagement', 'sessionTimeout', 24)

  const getTwoFactorSetting = () => getSetting('security', 'twoFactor', 'Optional')
  const getLoginAttemptsLimit = () => getSetting('security', 'loginAttempts', 5)
  const getLockoutDuration = () => getSetting('security', 'lockoutDuration', 30)

  const areNewRegistrationAlertsEnabled = () => getSetting('notifications', 'newRegistrationAlerts', true)
  const isDailySummaryEnabled = () => getSetting('notifications', 'dailySummary', true)
  const getEmailNotifications = () => getSetting('notifications', 'emailNotifications', '<EMAIL>')

  return {
    settings,
    loading,
    error,
    refetch: fetchSettings,
    getSetting,
    
    // System settings
    getSystemName,
    getTimezone,
    getDateFormat,
    isMaintenanceMode,
    isDebugMode,
    
    // User management settings
    getDefaultRole,
    isSelfRegistrationEnabled,
    getPasswordRequirements,
    getSessionTimeout,
    
    // Security settings
    getTwoFactorSetting,
    getLoginAttemptsLimit,
    getLockoutDuration,
    
    // Notification settings
    areNewRegistrationAlertsEnabled,
    isDailySummaryEnabled,
    getEmailNotifications
  }
}

