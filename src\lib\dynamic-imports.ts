import dynamic from 'next/dynamic'
import { ComponentType } from 'react'

// Loading component for dynamic imports
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
    <span className="ml-3 font-apercu-medium text-gray-600">Loading...</span>
  </div>
)

// Error boundary for dynamic imports
const ErrorFallback = ({ error }: { error: Error }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <div className="text-red-500 mb-2">⚠️</div>
      <p className="font-apercu-medium text-gray-900 mb-1">Failed to load component</p>
      <p className="font-apercu-regular text-sm text-gray-600">{error.message}</p>
    </div>
  </div>
)

// Dynamic import wrapper with consistent loading and error handling
export function createDynamicComponent<T = {}>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  options?: {
    loading?: ComponentType
    error?: ComponentType<{ error: Error }>
    ssr?: boolean
  }
) {
  return dynamic(importFn, {
    loading: options?.loading || LoadingSpinner,
    ssr: options?.ssr ?? true
  })
}

// Pre-configured dynamic imports for heavy components
export const DynamicComponents = {
  // Charts and analytics (heavy libraries)
  AnalyticsChart: createDynamicComponent(
    () => import('@/components/admin/AnalyticsChart'),
    { ssr: false }
  ),
  
  // Rich text editor (heavy)
  RichTextEditor: createDynamicComponent(
    () => import('@/components/ui/rich-text-editor'),
    { ssr: false }
  ),
  
  // PDF viewer (heavy)
  PDFViewer: createDynamicComponent(
    () => import('@/components/ui/pdf-viewer'),
    { ssr: false }
  ),
  
  // Calendar component (heavy)
  Calendar: createDynamicComponent(
    () => import('@/components/ui/calendar'),
    { ssr: false }
  ),
  
  // Data table with advanced features (heavy)
  AdvancedDataTable: createDynamicComponent(
    () => import('@/components/ui/advanced-data-table'),
    { ssr: false }
  ),
  
  // Image cropper (heavy)
  ImageCropper: createDynamicComponent(
    () => import('@/components/ui/image-cropper'),
    { ssr: false }
  ),
  
  // Code editor (heavy)
  CodeEditor: createDynamicComponent(
    () => import('@/components/ui/code-editor'),
    { ssr: false }
  ),
  
  // Map component (heavy)
  MapComponent: createDynamicComponent(
    () => import('@/components/ui/map'),
    { ssr: false }
  )
}

// Preload utilities for better UX
export const preloadComponent = {
  analytics: () => import('@/components/admin/AnalyticsChart'),
  richTextEditor: () => import('@/components/ui/rich-text-editor'),
  pdfViewer: () => import('@/components/ui/pdf-viewer'),
  calendar: () => import('@/components/ui/calendar'),
  advancedDataTable: () => import('@/components/ui/advanced-data-table'),
  imageCropper: () => import('@/components/ui/image-cropper'),
  codeEditor: () => import('@/components/ui/code-editor'),
  map: () => import('@/components/ui/map')
}

// Route-based code splitting
export const DynamicPages = {
  // Admin pages
  Dashboard: createDynamicComponent(() => import('@/app/admin/dashboard/page')),
  Registrations: createDynamicComponent(() => import('@/app/admin/registrations/page')),
  Accommodations: createDynamicComponent(() => import('@/app/admin/accommodations/page')),
  Inbox: createDynamicComponent(() => import('@/app/admin/inbox/page')),
  UserManagement: createDynamicComponent(() => import('@/app/admin/user-management/page')),
  Settings: createDynamicComponent(() => import('@/app/admin/settings/page')),
  Analytics: createDynamicComponent(() => import('@/app/admin/analytics/page')),
  
  // Modal components
  RoomModal: createDynamicComponent(() => import('@/components/admin/RoomModal')),
  UserModal: createDynamicComponent(() => import('@/components/admin/UserModal')),
  MessageModal: createDynamicComponent(() => import('@/components/admin/MessageModal')),
  RegistrationModal: createDynamicComponent(() => import('@/components/admin/RegistrationModal'))
}

// Intersection Observer for lazy loading
export class LazyLoader {
  private observer: IntersectionObserver | null = null
  private loadedComponents = new Set<string>()

  constructor() {
    if (typeof window !== 'undefined') {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const componentName = entry.target.getAttribute('data-component')
              if (componentName && !this.loadedComponents.has(componentName)) {
                this.loadComponent(componentName)
                this.loadedComponents.add(componentName)
                this.observer?.unobserve(entry.target)
              }
            }
          })
        },
        {
          rootMargin: '100px' // Start loading 100px before the component comes into view
        }
      )
    }
  }

  observe(element: Element, componentName: string) {
    if (this.observer) {
      element.setAttribute('data-component', componentName)
      this.observer.observe(element)
    }
  }

  private loadComponent(componentName: string) {
    const preloader = preloadComponent[componentName as keyof typeof preloadComponent]
    if (preloader) {
      preloader().catch(console.error)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

// Hook for lazy loading
export function useLazyLoader() {
  const loader = new LazyLoader()
  
  return {
    observe: loader.observe.bind(loader),
    disconnect: loader.disconnect.bind(loader)
  }
}

// Performance monitoring
export function measureComponentLoad(componentName: string) {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const loadTime = endTime - startTime
      
      // Log slow components (> 100ms)
      if (loadTime > 100) {
        console.warn(`Slow component load: ${componentName} took ${loadTime.toFixed(2)}ms`)
      }
      
      // Send to analytics if available
      if ('gtag' in window) {
        (window as any).gtag('event', 'component_load_time', {
          component_name: componentName,
          load_time: Math.round(loadTime)
        })
      }
    }
  }
  
  return () => {}
}

// Bundle analyzer helper
export function logBundleInfo() {
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Performance Tips:')
    console.log('• Use dynamic imports for heavy components')
    console.log('• Implement virtual scrolling for large lists')
    console.log('• Use React Query for caching')
    console.log('• Optimize images with Next.js Image component')
    console.log('• Enable gzip compression on server')
  }
}
