// Email template functions for the messaging system

interface MessageEmailProps {
  subject: string
  message: string
  senderName: string
  senderEmail: string
  recipientName: string
  isReply?: boolean
  originalSubject?: string
}

export function generateMessageEmail({
  subject,
  message,
  senderName,
  senderEmail,
  recipientName,
  isReply = false,
  originalSubject
}: MessageEmailProps): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${isReply ? 'Reply' : 'New Message'}</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content { 
            padding: 30px; 
        }
        .greeting {
            font-size: 18px;
            color: #667eea;
            margin: 0 0 20px 0;
            font-weight: 600;
        }
        .sender-info { 
            background: #f8fafc; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .sender-info h3 {
            margin: 0 0 10px 0;
            color: #374151;
            font-size: 16px;
            font-weight: 600;
        }
        .sender-info p {
            margin: 0;
            color: #6b7280;
        }
        .sender-name {
            font-weight: 600;
            color: #111827;
            font-size: 16px;
        }
        .message-box { 
            background: #ffffff; 
            padding: 24px; 
            border-radius: 8px; 
            border: 1px solid #e5e7eb;
            margin: 20px 0; 
        }
        .message-subject {
            margin: 0 0 16px 0;
            color: #667eea;
            font-size: 18px;
            font-weight: 600;
        }
        .message-content {
            white-space: pre-wrap;
            line-height: 1.6;
            color: #374151;
            font-size: 15px;
        }
        .reply-context { 
            background: #f0f9ff; 
            padding: 16px; 
            border-radius: 6px; 
            margin: 16px 0; 
            border-left: 3px solid #0ea5e9;
        }
        .reply-context h4 {
            margin: 0 0 8px 0;
            color: #0ea5e9;
            font-size: 14px;
            font-weight: 600;
        }
        .reply-context p {
            margin: 0;
            font-style: italic;
            color: #64748b;
            font-size: 14px;
        }
        .action-button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .action-button:hover {
            transform: translateY(-1px);
        }
        .footer { 
            background: #f9fafb;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
            font-size: 12px;
            color: #6b7280;
        }
        .logo {
            font-size: 20px;
            margin-right: 8px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            .header, .content {
                padding: 20px;
            }
            .message-box {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <span class="logo">${isReply ? '↩️' : '📨'}</span>
                ${isReply ? 'Reply Received' : 'New Message'}
            </h1>
            <p>${isReply ? 'You have received a reply to your message' : 'You have received a new message'}</p>
        </div>

        <div class="content">
            <h2 class="greeting">Hello ${recipientName},</h2>

            ${isReply && originalSubject ? `
            <div class="reply-context">
                <h4>💬 In reply to:</h4>
                <p>"${originalSubject}"</p>
            </div>
            ` : ''}

            <div class="sender-info">
                <h3>${isReply ? 'Reply from:' : 'Message from:'}</h3>
                <p class="sender-name">${senderName}</p>
                <p>${senderEmail}</p>
            </div>

            <div class="message-box">
                <h3 class="message-subject">Subject: ${subject}</h3>
                <div class="message-content">${message}</div>
            </div>

            <p style="margin-top: 24px;">
                <a href="mailto:${senderEmail}?subject=Re: ${encodeURIComponent(subject)}" class="action-button">
                    Reply to ${senderName}
                </a>
            </p>

            <p style="margin-top: 16px; color: #6b7280; font-size: 14px;">
                You can also reply through the Youth Registration System dashboard for better message tracking.
            </p>
        </div>

        <div class="footer">
            <p><strong>Youth Registration System</strong></p>
            <p>This message was sent through the Youth Registration System messaging platform.</p>
            <p>Please do not reply to this email directly. Use the reply button above or log into the system to respond.</p>
        </div>
    </div>
</body>
</html>`
}

interface NotificationEmailProps {
  recipientName: string
  title: string
  message: string
  actionUrl?: string
  actionText?: string
  type?: 'info' | 'success' | 'warning' | 'error'
}

export function generateNotificationEmail({
  recipientName,
  title,
  message,
  actionUrl,
  actionText = 'View Details',
  type = 'info'
}: NotificationEmailProps): string {
  const typeColors = {
    info: { bg: '#3b82f6', light: '#dbeafe' },
    success: { bg: '#10b981', light: '#d1fae5' },
    warning: { bg: '#f59e0b', light: '#fef3c7' },
    error: { bg: '#ef4444', light: '#fee2e2' }
  }

  const typeIcons = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  }

  const colors = typeColors[type]
  const icon = typeIcons[type]

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: ${colors.bg}; 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .content { 
            padding: 30px; 
        }
        .notification-box {
            background: ${colors.light};
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid ${colors.bg};
            margin: 20px 0;
        }
        .action-button {
            display: inline-block;
            padding: 12px 24px;
            background: ${colors.bg};
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 600;
        }
        .footer { 
            background: #f9fafb;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${icon} ${title}</h1>
        </div>

        <div class="content">
            <h2>Hello ${recipientName},</h2>

            <div class="notification-box">
                <p>${message}</p>
            </div>

            ${actionUrl ? `
            <p>
                <a href="${actionUrl}" class="action-button">${actionText}</a>
            </p>
            ` : ''}
        </div>

        <div class="footer">
            <p><strong>Youth Registration System</strong></p>
            <p>This is an automated notification from the Youth Registration System.</p>
        </div>
    </div>
</body>
</html>`
}

interface WelcomeEmailProps {
  userName: string
  userEmail: string
  temporaryPassword?: string
  loginUrl: string
}

export function generateWelcomeEmail({
  userName,
  userEmail,
  temporaryPassword,
  loginUrl
}: WelcomeEmailProps): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Youth Registration System</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 40px 30px; 
            text-align: center; 
        }
        .content { 
            padding: 30px; 
        }
        .credentials-box {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        .action-button {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 600;
            font-size: 16px;
        }
        .footer { 
            background: #f9fafb;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to Youth Registration System!</h1>
            <p>Your account has been created successfully</p>
        </div>

        <div class="content">
            <h2>Hello ${userName},</h2>
            
            <p>Welcome to the Youth Registration System! Your account has been created and you can now access the platform.</p>

            <div class="credentials-box">
                <h3>Your Login Credentials:</h3>
                <p><strong>Email:</strong> ${userEmail}</p>
                ${temporaryPassword ? `<p><strong>Temporary Password:</strong> ${temporaryPassword}</p>` : ''}
                <p style="color: #dc2626; font-size: 14px; margin-top: 10px;">
                    ${temporaryPassword ? '⚠️ Please change your password after your first login for security.' : ''}
                </p>
            </div>

            <p>
                <a href="${loginUrl}" class="action-button">Login to Your Account</a>
            </p>

            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
        </div>

        <div class="footer">
            <p><strong>Youth Registration System</strong></p>
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>`
}
