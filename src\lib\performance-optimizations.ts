// Performance optimization utilities for faster page loading

import { NextRequest, NextResponse } from 'next/server'

// Cache configuration for different data types
export const CACHE_DURATIONS = {
  // Static data that rarely changes
  STATIC: 60 * 60 * 24, // 24 hours
  
  // User data that changes occasionally
  USER_DATA: 60 * 15, // 15 minutes
  
  // Messages and notifications (frequently changing)
  MESSAGES: 60 * 2, // 2 minutes
  
  // Analytics data (changes daily)
  ANALYTICS: 60 * 60, // 1 hour
  
  // Registration data (changes frequently)
  REGISTRATIONS: 60 * 5, // 5 minutes
  
  // Room data (changes occasionally)
  ROOMS: 60 * 10, // 10 minutes
} as const

// Response headers for caching
export function getCacheHeaders(duration: number) {
  return {
    'Cache-Control': `public, s-maxage=${duration}, stale-while-revalidate=${duration * 2}`,
    'CDN-Cache-Control': `public, s-maxage=${duration}`,
    'Vercel-CDN-Cache-Control': `public, s-maxage=${duration}`,
  }
}

// Optimized response wrapper
export function createOptimizedResponse(data: any, cacheSeconds: number = CACHE_DURATIONS.USER_DATA) {
  const response = NextResponse.json(data)
  
  // Add cache headers
  const headers = getCacheHeaders(cacheSeconds)
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  // Add performance headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  return response
}

// Database query optimization helpers
export const DB_OPTIMIZATIONS = {
  // Common select fields for different entities
  USER_FIELDS: {
    id: true,
    name: true,
    email: true,
    type: true,
    createdAt: true,
    role: {
      select: {
        id: true,
        name: true,
        permissions: true
      }
    }
  },
  
  MESSAGE_FIELDS: {
    id: true,
    subject: true,
    content: true,
    senderEmail: true,
    senderName: true,
    senderType: true,
    recipientName: true,
    recipientEmail: true,
    recipientType: true,
    sentAt: true,
    readAt: true,
    createdAt: true,
    threadId: true,
    parentId: true,
    status: true
  },
  
  REGISTRATION_FIELDS: {
    id: true,
    fullName: true,
    dateOfBirth: true,
    gender: true,
    emailAddress: true,
    phoneNumber: true,
    address: true,
    emergencyContactName: true,
    emergencyContactPhone: true,
    parentGuardianName: true,
    parentGuardianPhone: true,
    createdAt: true
  },
  
  NOTIFICATION_FIELDS: {
    id: true,
    type: true,
    title: true,
    message: true,
    priority: true,
    isRead: true,
    createdAt: true,
    authorizedBy: true,
    authorizedByEmail: true
  }
} as const

// Pagination helpers
export function getPaginationParams(searchParams: URLSearchParams) {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.min(100, Math.max(10, parseInt(searchParams.get('limit') || '50')))
  const skip = (page - 1) * limit
  
  return { page, limit, skip }
}

export function createPaginationResponse(data: any[], totalCount: number, page: number, limit: number) {
  return {
    data,
    pagination: {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit),
      hasNext: page < Math.ceil(totalCount / limit),
      hasPrev: page > 1
    }
  }
}

// Parallel query execution helper
export async function executeParallelQueries<T extends Record<string, Promise<any>>>(
  queries: T
): Promise<{ [K in keyof T]: Awaited<T[K]> }> {
  const keys = Object.keys(queries) as (keyof T)[]
  const promises = Object.values(queries)
  
  const results = await Promise.all(promises)
  
  return keys.reduce((acc, key, index) => {
    acc[key] = results[index]
    return acc
  }, {} as { [K in keyof T]: Awaited<T[K]> })
}

// Memory optimization for large datasets
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

// Request deduplication for identical API calls
const requestCache = new Map<string, { promise: Promise<any>; timestamp: number }>()
const DEDUP_TIMEOUT = 1000 // 1 second

export function deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
  const now = Date.now()
  const cached = requestCache.get(key)
  
  // If we have a recent request for the same key, return it
  if (cached && (now - cached.timestamp) < DEDUP_TIMEOUT) {
    return cached.promise
  }
  
  // Create new request
  const promise = requestFn().finally(() => {
    // Clean up after request completes
    setTimeout(() => requestCache.delete(key), DEDUP_TIMEOUT)
  })
  
  requestCache.set(key, { promise, timestamp: now })
  return promise
}

// Performance monitoring
export function measurePerformance<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  
  return fn().finally(() => {
    const duration = performance.now() - start
    
    // Log slow operations
    if (duration > 1000) {
      console.warn(`🐌 Slow operation: ${operation} took ${duration.toFixed(2)}ms`)
    } else if (duration > 500) {
      console.log(`⚠️ Moderate operation: ${operation} took ${duration.toFixed(2)}ms`)
    }
    
    // Send to monitoring service if available
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', 'api_performance', {
        operation,
        duration: Math.round(duration)
      })
    }
  })
}

// Error handling with performance context
export function handleApiError(error: any, operation: string) {
  console.error(`❌ Error in ${operation}:`, error)
  
  // Return appropriate error response
  if (error.code === 'P2002') {
    return NextResponse.json(
      { error: 'Duplicate entry', operation },
      { status: 409 }
    )
  }
  
  if (error.code === 'P2025') {
    return NextResponse.json(
      { error: 'Record not found', operation },
      { status: 404 }
    )
  }
  
  return NextResponse.json(
    { 
      error: 'Internal server error', 
      operation,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    },
    { status: 500 }
  )
}

// Batch operations for better performance
export async function batchProcess<T, R>(
  items: T[],
  processor: (batch: T[]) => Promise<R[]>,
  batchSize: number = 50
): Promise<R[]> {
  const chunks = chunkArray(items, batchSize)
  const results: R[] = []
  
  for (const chunk of chunks) {
    const chunkResults = await processor(chunk)
    results.push(...chunkResults)
  }
  
  return results
}

// Connection pool optimization
export const CONNECTION_LIMITS = {
  MAX_CONNECTIONS: 10,
  IDLE_TIMEOUT: 30000, // 30 seconds
  CONNECT_TIMEOUT: 5000, // 5 seconds
} as const

// Response compression helper
export function shouldCompress(data: any): boolean {
  const jsonString = JSON.stringify(data)
  return jsonString.length > 1024 // Compress responses larger than 1KB
}

// API rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(identifier: string, limit: number = 100, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < windowStart) {
      rateLimitMap.delete(key)
    }
  }
  
  const current = rateLimitMap.get(identifier)
  
  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (current.resetTime < now) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (current.count >= limit) {
    return false
  }
  
  current.count++
  return true
}

// Health check helper
export function createHealthCheck() {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  }
}
