import React, { useCallback, useMemo, useRef, useEffect, useState } from 'react'

// Debounce hook for search inputs
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttle hook for scroll events
export function useThrottle<T>(value: T, delay: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastRan = useRef(Date.now())

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= delay) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, delay - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return throttledValue
}

// Virtual scrolling hook for large lists
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0)
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    }
  }, [items, itemHeight, containerHeight, scrollTop])

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    ...visibleItems,
    handleScroll
  }
}

// Memoized component wrapper
export function withMemo<P extends object>(
  Component: React.ComponentType<P>,
  areEqual?: (prevProps: P, nextProps: P) => boolean
) {
  return React.memo(Component, areEqual)
}

// Lazy loading hook for images
export function useLazyLoad() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const handleLoad = useCallback(() => {
    setIsLoaded(true)
  }, [])

  return {
    imgRef,
    isLoaded,
    isInView,
    handleLoad
  }
}

// Optimized search function
export function createOptimizedSearch<T>(
  items: T[],
  searchFields: (keyof T)[],
  options: {
    caseSensitive?: boolean
    fuzzy?: boolean
    minLength?: number
  } = {}
) {
  const { caseSensitive = false, fuzzy = false, minLength = 1 } = options

  return useCallback((query: string): T[] => {
    if (!query || query.length < minLength) return items

    const searchTerm = caseSensitive ? query : query.toLowerCase()

    return items.filter(item => {
      return searchFields.some(field => {
        const value = item[field]
        if (typeof value !== 'string') return false
        
        const fieldValue = caseSensitive ? value : value.toLowerCase()
        
        if (fuzzy) {
          // Simple fuzzy search - checks if all characters exist in order
          let searchIndex = 0
          for (let i = 0; i < fieldValue.length && searchIndex < searchTerm.length; i++) {
            if (fieldValue[i] === searchTerm[searchIndex]) {
              searchIndex++
            }
          }
          return searchIndex === searchTerm.length
        } else {
          return fieldValue.includes(searchTerm)
        }
      })
    })
  }, [items, searchFields, caseSensitive, fuzzy, minLength])
}

// Pagination hook with performance optimizations
export function useOptimizedPagination<T>(
  items: T[],
  itemsPerPage: number = 10
) {
  const [currentPage, setCurrentPage] = useState(1)
  
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    
    return {
      items: items.slice(startIndex, endIndex),
      totalPages: Math.ceil(items.length / itemsPerPage),
      totalItems: items.length,
      currentPage,
      hasNextPage: currentPage < Math.ceil(items.length / itemsPerPage),
      hasPrevPage: currentPage > 1,
      startIndex: startIndex + 1,
      endIndex: Math.min(endIndex, items.length)
    }
  }, [items, currentPage, itemsPerPage])

  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, paginatedData.totalPages)))
  }, [paginatedData.totalPages])

  const nextPage = useCallback(() => {
    if (paginatedData.hasNextPage) {
      setCurrentPage(prev => prev + 1)
    }
  }, [paginatedData.hasNextPage])

  const prevPage = useCallback(() => {
    if (paginatedData.hasPrevPage) {
      setCurrentPage(prev => prev - 1)
    }
  }, [paginatedData.hasPrevPage])

  return {
    ...paginatedData,
    goToPage,
    nextPage,
    prevPage,
    setCurrentPage
  }
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0)
  const startTime = useRef(Date.now())

  useEffect(() => {
    renderCount.current++
    const endTime = Date.now()
    const renderTime = endTime - startTime.current
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} - Render #${renderCount.current} - Time: ${renderTime}ms`)
    }
    
    startTime.current = endTime
  })

  return {
    renderCount: renderCount.current
  }
}

// Cache hook for expensive computations
export function useCache<T>(
  key: string,
  computeFn: () => T,
  deps: React.DependencyList
): T {
  const cache = useRef<Map<string, T>>(new Map())
  
  return useMemo(() => {
    if (cache.current.has(key)) {
      return cache.current.get(key)!
    }
    
    const result = computeFn()
    cache.current.set(key, result)
    return result
  }, [key, ...deps])
}
