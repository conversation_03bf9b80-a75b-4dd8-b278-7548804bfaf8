import { QueryClient, QueryClientProvider, useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

// Create a client with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true
    },
    mutations: {
      retry: 1
    }
  }
})

// Query keys for consistent caching
export const queryKeys = {
  // Messages
  messages: {
    all: ['messages'] as const,
    inbox: (page = 1, limit = 20) => ['messages', 'inbox', { page, limit }] as const,
    sent: (page = 1, limit = 20) => ['messages', 'sent', { page, limit }] as const,
    deleted: (page = 1, limit = 20) => ['messages', 'deleted', { page, limit }] as const,
    thread: (threadId: string) => ['messages', 'thread', threadId] as const,
    stats: () => ['messages', 'stats'] as const
  },
  
  // Accommodations
  accommodations: {
    all: ['accommodations'] as const,
    rooms: () => ['accommodations', 'rooms'] as const,
    unallocated: () => ['accommodations', 'unallocated'] as const,
    stats: () => ['accommodations', 'stats'] as const
  },
  
  // Registrations
  registrations: {
    all: ['registrations'] as const,
    list: (page = 1, limit = 20, filters?: any) => ['registrations', 'list', { page, limit, filters }] as const,
    detail: (id: string) => ['registrations', 'detail', id] as const,
    stats: () => ['registrations', 'stats'] as const
  },
  
  // Notifications
  notifications: {
    all: ['notifications'] as const,
    list: (page = 1, limit = 20) => ['notifications', 'list', { page, limit }] as const,
    unread: () => ['notifications', 'unread'] as const
  },
  
  // Users
  users: {
    all: ['users'] as const,
    list: (page = 1, limit = 20) => ['users', 'list', { page, limit }] as const,
    detail: (id: string) => ['users', 'detail', id] as const,
    current: () => ['users', 'current'] as const
  }
}

// Custom hooks for common queries
export const useInboxMessages = (page = 1, limit = 20) => {
  return useQuery({
    queryKey: queryKeys.messages.inbox(page, limit),
    queryFn: async () => {
      const response = await fetch(`/api/admin/messages/inbox?page=${page}&limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch inbox messages')
      }
      return response.json()
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for messages
    gcTime: 5 * 60 * 1000 // 5 minutes
  })
}

export const useSentMessages = (page = 1, limit = 20) => {
  return useQuery({
    queryKey: queryKeys.messages.sent(page, limit),
    queryFn: async () => {
      const response = await fetch(`/api/admin/messages/sent?page=${page}&limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch sent messages')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000 // 5 minutes for sent messages
  })
}

export const useDeletedMessages = (page = 1, limit = 20) => {
  return useQuery({
    queryKey: queryKeys.messages.deleted(page, limit),
    queryFn: async () => {
      const response = await fetch(`/api/admin/messages/deleted?page=${page}&limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch deleted messages')
      }
      return response.json()
    },
    staleTime: 10 * 60 * 1000 // 10 minutes for deleted messages
  })
}

export const useAccommodations = () => {
  return useQuery({
    queryKey: queryKeys.accommodations.all,
    queryFn: async () => {
      const response = await fetch('/api/admin/accommodations')
      if (!response.ok) {
        throw new Error('Failed to fetch accommodations')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000 // 15 minutes
  })
}

export const useRegistrations = (page = 1, limit = 20, filters?: any) => {
  return useQuery({
    queryKey: queryKeys.registrations.list(page, limit, filters),
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(filters && Object.keys(filters).length > 0 ? { filters: JSON.stringify(filters) } : {})
      })
      
      const response = await fetch(`/api/admin/registrations?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch registrations')
      }
      return response.json()
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}

// Mutation hooks for common operations
export const useMarkMessageAsRead = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (messageId: string) => {
      const response = await fetch(`/api/admin/messages/${messageId}/read`, {
        method: 'POST'
      })
      if (!response.ok) {
        throw new Error('Failed to mark message as read')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch message queries
      queryClient.invalidateQueries({ queryKey: queryKeys.messages.all })
    }
  })
}

export const useDeleteMessage = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ messageId, permanent = false }: { messageId: string, permanent?: boolean }) => {
      const url = `/api/admin/messages/${messageId}/delete${permanent ? '?permanent=true' : ''}`
      const response = await fetch(url, {
        method: 'DELETE'
      })
      if (!response.ok) {
        throw new Error('Failed to delete message')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch message queries
      queryClient.invalidateQueries({ queryKey: queryKeys.messages.all })
    }
  })
}

export const useSendReply = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ messageId, content }: { messageId: string, content: string }) => {
      const response = await fetch(`/api/admin/messages/${messageId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content })
      })
      if (!response.ok) {
        throw new Error('Failed to send reply')
      }
      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch message queries
      queryClient.invalidateQueries({ queryKey: queryKeys.messages.all })
    }
  })
}

// Prefetch utilities
export const prefetchInboxMessages = (page = 1, limit = 20) => {
  return queryClient.prefetchQuery({
    queryKey: queryKeys.messages.inbox(page, limit),
    queryFn: async () => {
      const response = await fetch(`/api/admin/messages/inbox?page=${page}&limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch inbox messages')
      }
      return response.json()
    },
    staleTime: 2 * 60 * 1000
  })
}

// Cache invalidation utilities
export const invalidateMessages = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.messages.all })
}

export const invalidateAccommodations = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.accommodations.all })
}

export const invalidateRegistrations = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.registrations.all })
}
