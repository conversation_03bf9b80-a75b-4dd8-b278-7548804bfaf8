import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Cache for settings to avoid database calls
let settingsCache: Record<string, any> = {}
let cacheTimestamp = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export async function getSetting(category: string, key: string, defaultValue: any = null) {
  try {
    // Check cache first
    const cacheKey = `${category}.${key}`
    const now = Date.now()

    if (settingsCache[cacheKey] && (now - cacheTimestamp) < CACHE_DURATION) {
      return settingsCache[cacheKey]
    }

    // Fetch from database
    const setting = await prisma.setting.findUnique({
      where: {
        category_key: {
          category,
          key
        }
      }
    })

    if (!setting) {
      return defaultValue
    }

    // Parse JSON value
    let parsedValue
    try {
      parsedValue = JSON.parse(setting.value)
    } catch {
      parsedValue = setting.value
    }

    // Update cache
    settingsCache[cacheKey] = parsedValue
    cacheTimestamp = now

    return parsedValue
  } catch (error) {
    console.error(`<PERSON>rror getting setting ${category}.${key}:`, error)
    return defaultValue
  }
}

export async function getSettings(category?: string) {
  try {
    const where = category ? { category } : {}

    const settings = await prisma.setting.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    })

    // Group by category and parse values
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = {}
      }

      // Parse JSON value
      let parsedValue
      try {
        parsedValue = JSON.parse(setting.value)
      } catch {
        parsedValue = setting.value
      }

      acc[setting.category][setting.key] = parsedValue
      return acc
    }, {} as Record<string, Record<string, any>>)

    return groupedSettings
  } catch (error) {
    console.error('Error getting settings:', error)
    return {}
  }
}

export async function updateSetting(category: string, key: string, value: any) {
  try {
    const setting = await prisma.setting.update({
      where: {
        category_key: {
          category,
          key
        }
      },
      data: {
        value: JSON.stringify(value),
        updatedAt: new Date()
      }
    })

    // Clear cache
    const cacheKey = `${category}.${key}`
    delete settingsCache[cacheKey]

    return setting
  } catch (error) {
    // In production, this should be logged to a proper logging system
    throw new Error(`Failed to update setting ${category}.${key}: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export function clearSettingsCache() {
  settingsCache = {}
  cacheTimestamp = 0
}

// Specific helper functions for common settings
export async function getDefaultUserRole() {
  return await getSetting('userManagement', 'defaultRole', 'Viewer')
}

export async function isSelfRegistrationEnabled() {
  return await getSetting('userManagement', 'selfRegistration', false)
}

export async function getPasswordRequirements() {
  return await getSetting('userManagement', 'passwordRequirements', 'Medium')
}

export async function getSessionTimeout() {
  return await getSetting('userManagement', 'sessionTimeout', 24)
}

export async function getMaxUsers() {
  return await getSetting('userManagement', 'maxUsers', 1000)
}

export async function getEmailNotifications() {
  return await getSetting('notifications', 'emailNotifications', '<EMAIL>')
}

export async function getTwoFactorSetting() {
  return await getSetting('security', 'twoFactor', 'Optional')
}

export async function getLoginAttemptsLimit() {
  return await getSetting('security', 'loginAttempts', 5)
}

export async function getLockoutDuration() {
  return await getSetting('security', 'lockoutDuration', 30)
}

export async function areNewRegistrationAlertsEnabled() {
  return await getSetting('notifications', 'newRegistrationAlerts', true)
}

export async function isDailySummaryEnabled() {
  return await getSetting('notifications', 'dailySummary', true)
}

export async function getSystemName() {
  return await getSetting('system', 'systemName', 'YouthConnect')
}

export async function getTimezone() {
  return await getSetting('system', 'timezone', 'UTC-5 (EST)')
}

export async function isMaintenanceMode() {
  return await getSetting('system', 'maintenanceMode', false)
}

export async function isDebugMode() {
  return await getSetting('system', 'debugMode', false)
}
