import { prisma } from '@/lib/db'

/**
 * Cleanup expired temporary access and restore original roles
 * This should be run periodically (e.g., every 5 minutes)
 */
export async function cleanupExpiredTemporaryAccess() {
  try {
    console.log('Starting temporary access cleanup...')

    // Find all expired temporary access records that are still active
    const expiredAccess = await prisma.temporaryAccess.findMany({
      where: {
        isActive: true,
        expiresAt: {
          lte: new Date()
        }
      },
      include: {
        user: {
          include: { role: true }
        }
      }
    })

    if (expiredAccess.length === 0) {
      console.log('No expired temporary access found')
      return { cleaned: 0 }
    }

    console.log(`Found ${expiredAccess.length} expired temporary access records`)

    // Process each expired access
    for (const access of expiredAccess) {
      try {
        // Restore original role
        await prisma.user.update({
          where: { id: access.userId },
          data: { roleId: access.originalRoleId }
        })

        // Mark temporary access as inactive
        await prisma.temporaryAccess.update({
          where: { id: access.id },
          data: { 
            isActive: false,
            revokedAt: new Date(),
            revokedBy: 'system',
            revokedByName: 'System (Auto-expired)'
          }
        })

        // Create notification for the user
        await prisma.notification.create({
          data: {
            type: 'access_expired',
            title: 'Temporary Access Expired',
            message: `Your temporary ${access.temporaryRole} access has expired and your original permissions have been restored.`,
            priority: 'medium',
            recipientId: access.userId,
            metadata: JSON.stringify({
              temporaryAccessId: access.id,
              originalRole: access.user.role.name,
              expiredRole: access.temporaryRole
            })
          }
        })

        // Create audit log
        await prisma.notification.create({
          data: {
            type: 'system_action',
            title: 'Temporary Access Auto-Expired',
            message: `Temporary ${access.temporaryRole} access for ${access.user.name} has automatically expired`,
            priority: 'low',
            recipientId: null,
            authorizedBy: 'System',
            authorizedByEmail: 'system@auto',
            metadata: JSON.stringify({
              action: 'auto_expire_temporary_access',
              userId: access.userId,
              userName: access.user.name,
              temporaryRole: access.temporaryRole,
              originalRole: access.user.role.name,
              expiredAt: new Date().toISOString()
            })
          }
        })

        console.log(`Cleaned up expired temporary access for user ${access.user.name} (${access.temporaryRole})`)

      } catch (error) {
        console.error(`Error cleaning up temporary access for user ${access.userId}:`, error)
      }
    }

    console.log(`Temporary access cleanup completed. Cleaned ${expiredAccess.length} records.`)
    return { cleaned: expiredAccess.length }

  } catch (error) {
    console.error('Error during temporary access cleanup:', error)
    throw error
  }
}

/**
 * Get all active temporary access records
 */
export async function getActiveTemporaryAccess() {
  try {
    const activeAccess = await prisma.temporaryAccess.findMany({
      where: {
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        expiresAt: 'asc'
      }
    })

    return activeAccess.map(access => ({
      id: access.id,
      userId: access.userId,
      userName: access.user.name,
      userEmail: access.user.email,
      temporaryRole: access.temporaryRole,
      grantedBy: access.grantedByName,
      reason: access.reason,
      grantedAt: access.createdAt,
      expiresAt: access.expiresAt,
      timeRemaining: Math.max(0, Math.floor((access.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60))), // hours
      minutesRemaining: Math.max(0, Math.floor((access.expiresAt.getTime() - Date.now()) / (1000 * 60))) // minutes
    }))

  } catch (error) {
    console.error('Error getting active temporary access:', error)
    throw error
  }
}

/**
 * Check if a user has active temporary access
 */
export async function hasActiveTemporaryAccess(userId: string): Promise<boolean> {
  try {
    const access = await prisma.temporaryAccess.findFirst({
      where: {
        userId,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    return !!access
  } catch (error) {
    console.error('Error checking temporary access:', error)
    return false
  }
}

/**
 * Get temporary access history for a user
 */
export async function getTemporaryAccessHistory(userId: string) {
  try {
    const history = await prisma.temporaryAccess.findMany({
      where: {
        userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return history.map(access => ({
      id: access.id,
      temporaryRole: access.temporaryRole,
      grantedBy: access.grantedByName,
      reason: access.reason,
      grantedAt: access.createdAt,
      expiresAt: access.expiresAt,
      revokedAt: access.revokedAt,
      revokedBy: access.revokedByName,
      isActive: access.isActive && access.expiresAt > new Date(),
      status: access.isActive && access.expiresAt > new Date() ? 'active' : 
              access.revokedAt ? 'revoked' : 'expired'
    }))

  } catch (error) {
    console.error('Error getting temporary access history:', error)
    throw error
  }
}

/**
 * Initialize cleanup interval (call this in your app startup)
 */
export function initializeTemporaryAccessCleanup() {
  // Run cleanup every 5 minutes
  const interval = setInterval(async () => {
    try {
      await cleanupExpiredTemporaryAccess()
    } catch (error) {
      console.error('Scheduled temporary access cleanup failed:', error)
    }
  }, 5 * 60 * 1000) // 5 minutes

  // Run initial cleanup
  cleanupExpiredTemporaryAccess().catch(error => {
    console.error('Initial temporary access cleanup failed:', error)
  })

  return interval
}
