const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function listUsers() {
  try {
    console.log('=== ADMINS ===')
    const admins = await prisma.admin.findMany({
      include: {
        role: true
      }
    })
    
    admins.forEach(admin => {
      console.log(`Admin: ${admin.email} | Name: ${admin.name} | Active: ${admin.isActive} | Role: ${admin.role?.name || 'No role'}`)
    })

    console.log('\n=== USERS ===')
    const users = await prisma.user.findMany({
      include: {
        role: true
      }
    })
    
    users.forEach(user => {
      console.log(`User: ${user.email} | Name: ${user.name} | Active: ${user.isActive} | Role: ${user.role?.name || 'No role'}`)
    })

    console.log(`\nTotal: ${admins.length} admins, ${users.length} users`)

  } catch (error) {
    console.error('Error listing users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

listUsers()
