const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const testNotifications = [
  {
    type: 'new_registration',
    title: 'New Registration Received',
    message: 'A new youth registration has been submitted and is pending review.',
    priority: 'medium',
    isRead: false,
    metadata: JSON.stringify({ registrationId: 'test-123' })
  },
  {
    type: 'system_maintenance',
    title: 'System Maintenance Scheduled',
    message: 'System maintenance is scheduled for tonight at 2:00 AM EST. Expected downtime: 30 minutes.',
    priority: 'high',
    isRead: false,
    metadata: JSON.stringify({ maintenanceWindow: '2024-01-15T02:00:00Z' })
  },
  {
    type: 'approval_required',
    title: 'Registration Approval Required',
    message: 'Multiple registrations are pending approval. Please review and approve or reject them.',
    priority: 'high',
    isRead: true,
    metadata: JSON.stringify({ pendingCount: 5 })
  },
  {
    type: 'report_ready',
    title: 'Monthly Report Generated',
    message: 'The monthly registration report for December 2024 has been generated and is ready for download.',
    priority: 'low',
    isRead: false,
    metadata: JSON.stringify({ reportType: 'monthly', month: 'December 2024' })
  },
  {
    type: 'email_sent',
    title: 'Confirmation Emails Sent',
    message: 'Registration confirmation emails have been sent to 15 participants.',
    priority: 'low',
    isRead: true,
    metadata: JSON.stringify({ emailCount: 15 })
  },
  {
    type: 'user_created',
    title: 'New User Account Created',
    message: 'A new staff user account has been created for John Smith.',
    priority: 'medium',
    isRead: false,
    metadata: JSON.stringify({ userName: 'John Smith', userRole: 'Staff' })
  },
  {
    type: 'security_alert',
    title: 'Multiple Failed Login Attempts',
    message: 'Multiple failed login attempts detected from IP address *************. Account has been temporarily locked.',
    priority: 'high',
    isRead: false,
    metadata: JSON.stringify({ ipAddress: '*************', attempts: 5 })
  },
  {
    type: 'backup_status',
    title: 'Database Backup Completed',
    message: 'Daily database backup completed successfully. Backup size: 2.3 GB.',
    priority: 'low',
    isRead: true,
    metadata: JSON.stringify({ backupSize: '2.3 GB', backupTime: '2024-01-14T03:00:00Z' })
  },
  {
    type: 'user_deleted',
    title: 'User Account Deleted',
    message: 'User account for Jane Doe has been permanently deleted from the system.',
    priority: 'medium',
    isRead: true,
    metadata: JSON.stringify({ deletedUser: 'Jane Doe', deletedBy: 'Admin' })
  },
  {
    type: 'new_registration',
    title: 'Registration Deadline Approaching',
    message: 'Registration deadline for Summer Camp 2024 is approaching in 7 days.',
    priority: 'medium',
    isRead: false,
    metadata: JSON.stringify({ deadline: '2024-01-21', daysRemaining: 7 })
  }
]

async function seedNotifications() {
  console.log('🔔 Seeding test notifications...')

  try {
    // Clear existing notifications
    await prisma.notification.deleteMany({})
    console.log('Cleared existing notifications')

    // Create test notifications with staggered timestamps
    for (let i = 0; i < testNotifications.length; i++) {
      const notification = testNotifications[i]
      const createdAt = new Date()
      createdAt.setHours(createdAt.getHours() - (i * 2)) // Stagger by 2 hours each

      await prisma.notification.create({
        data: {
          ...notification,
          createdAt
        }
      })
    }

    console.log(`✅ Created ${testNotifications.length} test notifications!`)
  } catch (error) {
    console.error('❌ Error seeding notifications:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

seedNotifications()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
