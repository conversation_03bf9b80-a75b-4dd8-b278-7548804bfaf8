const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const testRegistrations = [
  {
    fullName: '<PERSON>',
    dateOfBirth: new Date('2008-03-15'),
    gender: 'Female',
    address: '123 Oak Street, Springfield, IL 62701',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: '<PERSON>',
    emergencyContactRelationship: '<PERSON>',
    emergencyContactPhone: '(*************',
    parentGuardianName: '<PERSON>',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'Peanuts',
    specialNeeds: 'None',
    dietaryRestrictions: 'Vegetarian',
    parentalPermissionGranted: true,
    parentalPermissionDate: new Date()
  },
  {
    fullName: '<PERSON>',
    dateOfBirth: new Date('2009-07-22'),
    gender: 'Male',
    address: '456 Pine Avenue, Springfield, IL 62702',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: '<PERSON>',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'David Chen',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'Inhaler for asthma',
    allergies: 'None',
    specialNeeds: 'None',
    dietaryRestrictions: 'None',
    parentalPermissionGranted: false,
    parentalPermissionDate: null
  },
  {
    fullName: 'Sophia Rodriguez',
    dateOfBirth: new Date('2007-11-08'),
    gender: 'Female',
    address: '789 Maple Drive, Springfield, IL 62703',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Maria Rodriguez',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'Carlos Rodriguez',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'Shellfish',
    specialNeeds: 'None',
    dietaryRestrictions: 'Gluten-free',
    parentalPermissionGranted: true,
    parentalPermissionDate: new Date()
  },
  {
    fullName: 'Ethan Williams',
    dateOfBirth: new Date('2008-12-03'),
    gender: 'Male',
    address: '321 Elm Street, Springfield, IL 62704',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Jennifer Williams',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'Robert Williams',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'None',
    specialNeeds: 'Hearing aid',
    dietaryRestrictions: 'None',
    parentalPermissionGranted: false,
    parentalPermissionDate: null
  },
  {
    fullName: 'Ava Thompson',
    dateOfBirth: new Date('2009-05-17'),
    gender: 'Female',
    address: '654 Cedar Lane, Springfield, IL 62705',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Michelle Thompson',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'James Thompson',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'Dairy',
    specialNeeds: 'None',
    dietaryRestrictions: 'Lactose-free',
    parentalPermissionGranted: true,
    parentalPermissionDate: new Date()
  },
  {
    fullName: 'Noah Davis',
    dateOfBirth: new Date('2008-09-25'),
    gender: 'Male',
    address: '987 Birch Road, Springfield, IL 62706',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Amanda Davis',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'Kevin Davis',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'None',
    specialNeeds: 'None',
    dietaryRestrictions: 'None',
    parentalPermissionGranted: false,
    parentalPermissionDate: null
  },
  {
    fullName: 'Isabella Garcia',
    dateOfBirth: new Date('2007-04-12'),
    gender: 'Female',
    address: '147 Willow Street, Springfield, IL 62707',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Rosa Garcia',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'Miguel Garcia',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'None',
    allergies: 'None',
    specialNeeds: 'None',
    dietaryRestrictions: 'None',
    parentalPermissionGranted: true,
    parentalPermissionDate: new Date()
  },
  {
    fullName: 'Liam Anderson',
    dateOfBirth: new Date('2009-01-30'),
    gender: 'Male',
    address: '258 Spruce Avenue, Springfield, IL 62708',
    phoneNumber: '(*************',
    emailAddress: '<EMAIL>',
    emergencyContactName: 'Karen Anderson',
    emergencyContactRelationship: 'Mother',
    emergencyContactPhone: '(*************',
    parentGuardianName: 'Brian Anderson',
    parentGuardianPhone: '(*************',
    parentGuardianEmail: '<EMAIL>',
    medications: 'ADHD medication',
    allergies: 'None',
    specialNeeds: 'ADHD accommodations',
    dietaryRestrictions: 'None',
    parentalPermissionGranted: false,
    parentalPermissionDate: null
  }
]

async function seedRegistrations() {
  console.log('📝 Seeding test registrations...')

  try {
    // Clear existing registrations
    await prisma.registration.deleteMany({})
    console.log('Cleared existing registrations')

    // Create test registrations with staggered timestamps
    for (let i = 0; i < testRegistrations.length; i++) {
      const registration = testRegistrations[i]
      const createdAt = new Date()
      createdAt.setDate(createdAt.getDate() - i) // Stagger by 1 day each

      await prisma.registration.create({
        data: {
          ...registration,
          createdAt,
          updatedAt: createdAt
        }
      })
    }

    console.log(`✅ Created ${testRegistrations.length} test registrations!`)
  } catch (error) {
    console.error('❌ Error seeding registrations:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

seedRegistrations()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
