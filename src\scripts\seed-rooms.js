const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const sampleRooms = [
  // Male Rooms
  {
    name: 'Room Shiloh',
    gender: 'Male',
    capacity: 8,
    description: 'Large dormitory room with bunk beds and shared facilities'
  },
  {
    name: 'Room David',
    gender: 'Male',
    capacity: 6,
    description: 'Medium-sized room with individual beds'
  },
  {
    name: 'Room Solomon',
    gender: 'Male',
    capacity: 4,
    description: 'Smaller room for quieter participants'
  },
  {
    name: 'Room Joshua',
    gender: 'Male',
    capacity: 10,
    description: 'Large capacity room for group activities'
  },
  
  // Female Rooms
  {
    name: 'Room Ruth',
    gender: 'Female',
    capacity: 8,
    description: 'Spacious room with modern amenities'
  },
  {
    name: 'Room Esther',
    gender: 'Female',
    capacity: 6,
    description: 'Comfortable room with study area'
  },
  {
    name: 'Room Deborah',
    gender: 'Female',
    capacity: 4,
    description: 'Cozy room perfect for close-knit groups'
  },
  {
    name: 'Room Miriam',
    gender: 'Female',
    capacity: 10,
    description: 'Large room with excellent natural lighting'
  },
  {
    name: 'Room Hannah',
    gender: 'Female',
    capacity: 5,
    description: 'Mid-size room with private bathroom'
  }
]

async function seedRooms() {
  try {
    console.log('🏠 Seeding rooms...')
    
    // Clear existing rooms
    await prisma.roomAllocation.deleteMany()
    await prisma.room.deleteMany()
    
    // Create rooms
    for (const roomData of sampleRooms) {
      await prisma.room.create({
        data: roomData
      })
      console.log(`✅ Created room: ${roomData.name}`)
    }
    
    console.log('✅ Room seeding completed successfully!')
    
    // Display summary
    const totalRooms = await prisma.room.count()
    const maleRooms = await prisma.room.count({ where: { gender: 'Male' } })
    const femaleRooms = await prisma.room.count({ where: { gender: 'Female' } })
    const totalCapacity = await prisma.room.aggregate({
      _sum: { capacity: true }
    })
    
    console.log('\n📊 Room Summary:')
    console.log(`Total Rooms: ${totalRooms}`)
    console.log(`Male Rooms: ${maleRooms}`)
    console.log(`Female Rooms: ${femaleRooms}`)
    console.log(`Total Capacity: ${totalCapacity._sum.capacity} beds`)
    
  } catch (error) {
    console.error('❌ Error seeding rooms:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seed function
seedRooms()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
