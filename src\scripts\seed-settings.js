const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const defaultSettings = [
  // User Management Settings
  {
    category: 'userManagement',
    key: 'defaultRole',
    value: JSON.stringify('viewer'),
    type: 'select',
    options: JSON.stringify(['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer']),
    name: 'Default User Role',
    description: 'Default role assigned to new users',
    isSystem: true
  },
  {
    category: 'userManagement',
    key: 'selfRegistration',
    value: JSON.stringify(false),
    type: 'toggle',
    name: 'Allow Self Registration',
    description: 'Allow users to register themselves',
    isSystem: true
  },
  {
    category: 'userManagement',
    key: 'passwordRequirements',
    value: JSON.stringify('strong'),
    type: 'select',
    options: JSON.stringify(['Weak', 'Medium', 'Strong']),
    name: 'Password Requirements',
    description: 'Minimum password strength requirement',
    isSystem: true
  },
  {
    category: 'userManagement',
    key: 'sessionTimeout',
    value: JSON.stringify('24'),
    type: 'number',
    name: 'Session Timeout (hours)',
    description: 'How long users stay logged in',
    isSystem: true
  },
  {
    category: 'userManagement',
    key: 'maxUsers',
    value: JSON.stringify('1000'),
    type: 'number',
    name: 'Maximum Users',
    description: 'Maximum number of users allowed',
    isSystem: true
  },

  // Security Settings
  {
    category: 'security',
    key: 'twoFactor',
    value: JSON.stringify('optional'),
    type: 'select',
    options: JSON.stringify(['Disabled', 'Optional', 'Required']),
    name: 'Two-Factor Authentication',
    description: 'Two-factor authentication requirement',
    isSystem: true
  },
  {
    category: 'security',
    key: 'loginAttempts',
    value: JSON.stringify('5'),
    type: 'number',
    name: 'Login Attempts Limit',
    description: 'Maximum failed login attempts before lockout',
    isSystem: true
  },
  {
    category: 'security',
    key: 'lockoutDuration',
    value: JSON.stringify('30'),
    type: 'number',
    name: 'Account Lockout (minutes)',
    description: 'How long accounts are locked after failed attempts',
    isSystem: true
  },
  {
    category: 'security',
    key: 'passwordExpiry',
    value: JSON.stringify('90'),
    type: 'number',
    name: 'Password Expiry (days)',
    description: 'How often users must change passwords',
    isSystem: true
  },
  {
    category: 'security',
    key: 'ipWhitelist',
    value: JSON.stringify(false),
    type: 'toggle',
    name: 'IP Whitelist',
    description: 'Restrict access to specific IP addresses',
    isSystem: true
  },

  // Notification Settings
  {
    category: 'notifications',
    key: 'newRegistrationAlerts',
    value: JSON.stringify(true),
    type: 'toggle',
    name: 'New Registration Alerts',
    description: 'Send alerts when new registrations are submitted',
    isSystem: true
  },
  {
    category: 'notifications',
    key: 'dailySummary',
    value: JSON.stringify(true),
    type: 'toggle',
    name: 'Daily Summary Reports',
    description: 'Send daily summary reports to admins',
    isSystem: true
  },
  {
    category: 'notifications',
    key: 'maintenanceAlerts',
    value: JSON.stringify(true),
    type: 'toggle',
    name: 'System Maintenance Alerts',
    description: 'Send alerts before system maintenance',
    isSystem: true
  },
  {
    category: 'notifications',
    key: 'emailNotifications',
    value: JSON.stringify('<EMAIL>'),
    type: 'email',
    name: 'Email Notifications',
    description: 'Email address for system notifications',
    isSystem: true
  },
  {
    category: 'notifications',
    key: 'slackWebhook',
    value: JSON.stringify(''),
    type: 'text',
    name: 'Slack Webhook URL',
    description: 'Slack webhook for notifications',
    isSystem: false
  },

  // System Settings
  {
    category: 'system',
    key: 'systemName',
    value: JSON.stringify('YouthConnect'),
    type: 'text',
    name: 'System Name',
    description: 'Name displayed throughout the application',
    isSystem: true
  },
  {
    category: 'system',
    key: 'timezone',
    value: JSON.stringify('UTC-5 (EST)'),
    type: 'select',
    options: JSON.stringify(['UTC-8 (PST)', 'UTC-7 (MST)', 'UTC-6 (CST)', 'UTC-5 (EST)', 'UTC (GMT)']),
    name: 'Time Zone',
    description: 'Default timezone for the system',
    isSystem: true
  },
  {
    category: 'system',
    key: 'dateFormat',
    value: JSON.stringify('MM/DD/YYYY'),
    type: 'select',
    options: JSON.stringify(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
    name: 'Date Format',
    description: 'Default date format for display',
    isSystem: true
  },
  {
    category: 'system',
    key: 'language',
    value: JSON.stringify('English'),
    type: 'select',
    options: JSON.stringify(['English', 'Spanish', 'French']),
    name: 'Language',
    description: 'Default system language',
    isSystem: true
  },
  {
    category: 'system',
    key: 'maintenanceMode',
    value: JSON.stringify(false),
    type: 'toggle',
    name: 'Maintenance Mode',
    description: 'Put system in maintenance mode',
    isSystem: true
  },
  {
    category: 'system',
    key: 'debugMode',
    value: JSON.stringify(false),
    type: 'toggle',
    name: 'Debug Mode',
    description: 'Enable debug logging',
    isSystem: true
  }
]

async function seedSettings() {
  console.log('🌱 Seeding settings...')

  try {
    for (const setting of defaultSettings) {
      await prisma.setting.upsert({
        where: {
          category_key: {
            category: setting.category,
            key: setting.key
          }
        },
        update: {
          // Only update non-system fields to preserve user changes
          name: setting.name,
          description: setting.description,
          type: setting.type,
          options: setting.options
        },
        create: setting
      })
    }

    console.log('✅ Settings seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding settings:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

if (require.main === module) {
  seedSettings()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

module.exports = { seedSettings }
