# 🧪 Message System Testing Guide

## Issues Fixed

### ✅ **1. Missing Email Templates**
- **Problem**: `@/lib/email-templates` module not found
- **Solution**: Created comprehensive email templates with beautiful HTML designs
- **Files**: `src/lib/email-templates.ts`

### ✅ **2. API Response Format Inconsistency**
- **Problem**: Frontend expecting different response format than API returning
- **Solution**: Updated message context and inbox page to handle new API format
- **Files**: `src/contexts/MessageContext.tsx`, `src/app/admin/inbox/page.tsx`

### ✅ **3. Duplicate Email Template Function**
- **Problem**: Send API had its own email template function instead of using shared one
- **Solution**: Removed duplicate and imported from shared templates
- **Files**: `src/app/api/admin/messages/send/route.ts`

## 🔍 Testing Steps

### **Step 1: Check Console Logs**
1. Open browser developer tools (F12)
2. Go to Console tab
3. Navigate to `/admin/inbox`
4. Look for these log messages:
   ```
   📊 Fetching message stats...
   📊 Message stats API response: {...}
   📊 Calculated stats: {total: X, unread: Y, thisWeek: Z}
   ```

### **Step 2: Test Message Sending**
1. Go to `/admin/users` (User Management)
2. Click "Message" button on any user
3. Fill in:
   - **Subject**: "Test Message"
   - **Message**: "This is a test message to verify the system works"
4. Click "Send Message"
5. Check console for:
   ```
   Message send attempt by: [sender_email]
   Message data: {recipientId: "...", recipientType: "...", subject: "Test Message"}
   Message record created: [message_id]
   Email sent successfully
   Message send process completed successfully
   ```

### **Step 3: Verify Message Appears in Sent Tab**
1. Go to `/admin/inbox`
2. Click "Sent" tab
3. Look for your test message
4. Check console for:
   ```
   📤 Fetching sent messages...
   📤 Sent API response: [...]
   📤 Set sent messages: X
   ```

### **Step 4: Verify Recipient Gets Message**
1. Login as the recipient user
2. Go to `/admin/inbox`
3. Check "Inbox" tab for the message
4. Verify unread badge appears in sidebar
5. Check console for:
   ```
   📥 Fetching inbox messages...
   📥 Inbox API response: [...]
   📥 Set inbox messages: X
   ```

### **Step 5: Test Message Reading**
1. Click on the unread message
2. Verify it opens and displays correctly
3. Check that unread badge count decreases
4. Verify message appears as read (gray background)

## 🐛 Debugging Common Issues

### **Issue: Messages Not Appearing in Sent Tab**
**Possible Causes:**
1. API returning wrong format
2. Frontend not parsing response correctly
3. Database query filtering out messages

**Debug Steps:**
```javascript
// In browser console, test the sent API directly:
fetch('/api/admin/messages/sent')
  .then(r => r.json())
  .then(data => console.log('Sent API Response:', data))
```

### **Issue: Recipients Not Getting Messages**
**Possible Causes:**
1. User lookup failing in send API
2. Message not being saved to database
3. Recipient type mismatch

**Debug Steps:**
```javascript
// Check if user exists in database
fetch('/api/admin/users')
  .then(r => r.json())
  .then(data => console.log('Users:', data))
```

### **Issue: Badges Not Updating**
**Possible Causes:**
1. Message context not refreshing
2. Stats calculation incorrect
3. API response format mismatch

**Debug Steps:**
```javascript
// Manually refresh message stats
// In browser console:
window.location.reload() // Force refresh
```

## 🔧 Manual API Testing

### **Test Send Message API**
```bash
curl -X POST http://localhost:3000/api/admin/messages/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "recipientId": "<EMAIL>",
    "recipientType": "admin",
    "subject": "Test Message",
    "message": "This is a test message"
  }'
```

### **Test Inbox API**
```bash
curl -X GET http://localhost:3000/api/admin/messages/inbox \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **Test Sent API**
```bash
curl -X GET http://localhost:3000/api/admin/messages/sent \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 Expected API Responses

### **Inbox API Response**
```json
{
  "success": true,
  "messages": [
    {
      "id": "message_id",
      "subject": "Test Subject",
      "content": "Message content",
      "senderName": "Sender Name",
      "senderEmail": "<EMAIL>",
      "sentAt": "2024-01-01T00:00:00.000Z",
      "readAt": null,
      "threadId": null,
      "replies": []
    }
  ],
  "stats": {
    "total": 1,
    "unread": 1,
    "read": 0,
    "thisWeek": 1
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

### **Sent API Response**
```json
[
  {
    "id": "message_id",
    "subject": "Test Subject",
    "content": "Message content",
    "recipientName": "Recipient Name",
    "recipientEmail": "<EMAIL>",
    "sentAt": "2024-01-01T00:00:00.000Z",
    "readAt": null,
    "threadId": null,
    "replies": []
  }
]
```

## ✅ Success Indicators

### **Message Sending Success**
- ✅ Console shows "Message send process completed successfully"
- ✅ API returns `{"success": true, "messageId": "...", "message": "Message sent successfully"}`
- ✅ Message appears in sender's "Sent" tab
- ✅ Message appears in recipient's "Inbox" tab
- ✅ Unread badge count increases for recipient
- ✅ Email notification sent (check email logs)

### **Message Reading Success**
- ✅ Message opens when clicked
- ✅ Message marked as read in database
- ✅ Unread badge count decreases
- ✅ Message background changes to gray (read state)

### **Badge Updates Success**
- ✅ Sidebar shows correct unread count
- ✅ Badge updates in real-time
- ✅ Badge disappears when all messages read
- ✅ Badge persists across page refreshes

## 🚨 If Issues Persist

### **1. Check Database**
```sql
-- Check if messages are being created
SELECT * FROM messages ORDER BY sentAt DESC LIMIT 10;

-- Check message counts by user
SELECT recipientEmail, COUNT(*) as total, 
       SUM(CASE WHEN readAt IS NULL THEN 1 ELSE 0 END) as unread
FROM messages 
WHERE isDeleted = false 
GROUP BY recipientEmail;
```

### **2. Check Email Configuration**
- Verify email settings in environment variables
- Check email service logs
- Test email sending separately

### **3. Check User Authentication**
- Verify user is properly authenticated
- Check user permissions and roles
- Ensure user type is correctly set

### **4. Clear Browser Cache**
- Clear browser cache and cookies
- Try in incognito/private mode
- Refresh the page completely

## 📞 Support

If the messaging system still doesn't work after following this guide:

1. **Check all console logs** for error messages
2. **Test each API endpoint** individually
3. **Verify database schema** matches expectations
4. **Check user permissions** and authentication
5. **Review email configuration** if email notifications fail

The messaging system should now work correctly with:
- ✅ Messages appearing in sent tab for senders
- ✅ Messages appearing in inbox for recipients  
- ✅ Proper badge counts and updates
- ✅ Email notifications
- ✅ Real-time message stats
